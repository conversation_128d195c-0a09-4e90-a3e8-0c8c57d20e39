{"+/cwsayrqk": "<PERSON><PERSON> r<PERSON>", "+7sd9hoyZA": "<PERSON><PERSON><PERSON>", "/PgA81GVOD": "Modifier", "/bRGKhnXQ6": "Une nouvelle version est disponible. Elle sera téléchargée et installée automatiquement.", "0g8/VVdNuN": "Accédez à claude.ai/settings pour configurer votre profil, votre équipe et plus encore.", "0tZLEYF8mJ": "Développeur", "1HUTYwndT2": "<PERSON><PERSON><PERSON>", "1PfZLi/OV7": "Général", "1TJUzU26sO": "Envoyer des commentaires...", "25aCMlTDUq": "D<PERSON>marrer Claude automatiquement à la connexion", "3ML3xT+gEV": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3gG1j3kRBX": "Envoyer des commentaires...", "3unrKzH4zB": "<PERSON><PERSON><PERSON>", "4qP7MjrQfC": "Variables d'environnement", "5DUIVR3fVi": "À propos...", "6yv8ytK4El": "Vérifiez votre connexion réseau", "7fdcqxofEs": "<PERSON><PERSON><PERSON>", "7gSC+rZzXX": "Accédez rapidement à Claude, où que vous soyez.", "8YQEOfuaGO": "<PERSON><PERSON>", "9+afSO9e/t": "Échec de la vérification des mises à jour : {error}", "9uNxNtcrFI": "Installer", "CZwl8X2D85": "Options avancées", "CizRPROPWo": "<PERSON><PERSON>", "D43DeqP+2t": "Paramètres de Claude", "D4DyT6MmPy": "Impossible de charger les paramètres de l'application", "DQTgg21B7g": "Afficher l'application", "E9jYTa7AbX": "Zone de notification", "EfdnINFnIz": "<PERSON><PERSON><PERSON>", "GSG5S0ysrR": "Lancer au démarrage", "HeHYq6bbS2": "Claude peut recevoir des informations comme des invites et des pièces jointes depuis des serveurs spécialisés utilisant le Model Context Protocol.", "I5O68ogAtr": "Commencer", "JVwNvMZjVT": "<PERSON><PERSON>", "KAo3lt5Hv+": "<PERSON><PERSON>", "Ko/2Ml7mZG": "Actualiser cette page", "L32WRR6NOL": "<PERSON><PERSON><PERSON><PERSON>", "L717supPIA": "Paramètres", "LCWUQ/4Fu6": "<PERSON><PERSON><PERSON><PERSON>", "NZIwKxgxJ+": "Êtes-vous sûr.e de vouloir supprimer le serveur MCP « {serverKey} » ?", "Nmvo1ufAY5": "Impossible de se connecter à <PERSON>", "O3rtEd7aMd": "<PERSON><PERSON><PERSON>", "ODySlGptaj": "Paramètres...", "PH29MShDiy": "Suivant", "PW5U8NgTto": "Ouvrir le fichier journal MCP...", "PZtcoAOSsa": "<PERSON>e pas activer", "PbJ4jR0kv1": "Vous utilisez la dernière version.", "RTg057HE1D": "Afficher les outils de développement", "S3MXlbjkax": "Comment puis-je vous aider aujou<PERSON>'hui ?", "S3k5yXss2r": "Version {version}", "TH+W2Ad73P": "Couper", "UJCjEVPX6Q": "<PERSON><PERSON><PERSON>", "Vvus2ifAny": "Modifier la configuration", "W1pELwt/+a": "<PERSON> s<PERSON>exécute dans la zone de notification.", "WBvq3HlPae": "Dé<PERSON><PERSON> le racco<PERSON>ci", "WF1HSu0jAC": "<PERSON><PERSON><PERSON><PERSON><PERSON> le dossier des journaux", "WZe86KSdrM": "Ajouter au dictionnaire", "WlhIx7DfFO": "OK", "XPIoFTkh3e": "Aucune mise à jour disponible", "XZ36+EBE5/": "Zoom arri<PERSON>", "XinCguXCgN": "En savoir plus", "YTdYCYAf/Z": "<PERSON><PERSON><PERSON><PERSON> dans la barre de menus", "Z9g5m/V9Nq": "Zoom avant", "ZJZN1+KyJw": "Paramètres...", "aNmxuDcWaU": "<PERSON><PERSON><PERSON>", "aXdFLiVzjd": "Afficher la fenêtre principale", "arbRxbtBkP": "Précédent", "baGq3gy8z1": "Nouvelle conversation", "dKX0bpR+a2": "<PERSON><PERSON><PERSON>", "dLyz0Srosd": "Développeur", "fEeEFfSz4K": "Zoom avant (version alternative)", "fFJxOwJRj2": "Annuler", "fWDSQQgRO5": "Barre de menus", "iFRmqBsr1N": "À propos de Claude", "ilE9e0uxNN": "Actualiser", "j66cdL4EK5": "<PERSON>uv<PERSON>r la documentation", "jd5ZNrRMNP": "Vous êtes une des premières personnes à l’utiliser. Faites-nous part de vos commentaires.", "k+06oXbIas": "Afficher Claude dans la zone de notification", "kYwW0OsI4M": "<PERSON> est en version bêta.", "m3GfpKD1WX": "Actualiser", "mRXjxhS6p4": "Rechercher des mises à jour…", "ngLpGT7bUJ": "Impossible de charger les paramètres développeur", "oQuOiX24pp": "<PERSON><PERSON><PERSON>", "pWXxZASpOB": "Aide", "pgaCSv2/6H": "Arguments", "q4hs14B00V": "<PERSON><PERSON><PERSON> inconnue", "rNAd+HxSK4": "Ouvrir le fichier journal MCP", "rY99UXvTDU": "Copier l'image", "rdiPpQVqvY": "Le mode développeur permet d’accéder aux outils de développement et aux fonctionnalités de débogage. Cette option doit être activée uniquement si vous savez de quelle manière procéder.", "rwFEudHXey": "Une erreur est survenue lors de la lecture ou de l'analyse de claude_desktop_config.json : {error}", "sNnRQsIEYz": "Rechercher dans la page", "sZxWXq9BzJ": "Donner votre avis", "sys7RHphmL": "Commentaires", "tWutslc/9Z": "Activer", "u1/hT7oRQY": "<PERSON><PERSON><PERSON><PERSON> clavier de saisie rapide", "uc3dnSo+eo": "<PERSON><PERSON><PERSON>", "urCd4k/cE0": "Commande", "vgLHPxjh9O": "Activer le mode développeur", "wS64bVG2CO": "MCP est un protocole qui permet d’établir des connexions sécurisées entre les clients, comme l'application Claude Desktop, et les services locaux.", "xJs1jZ8PoA": "<PERSON> continue de fonctionner en arrière-plan, même lorsque vous fermez la fenêtre. Cliquez sur l’icône Claude dans la barre de notification pour rouvrir l’application, ou faites un clic droit pour quitter.", "xKRKzVVy9c": "Configurer", "xd436TVDRZ": "Une erreur est survenue lors de la lecture ou de l'analyse de developer_settings.json : {error}", "y9tCbmRzHN": "Activer le mode développeur ?", "ytjMRobdyL": "Mise à jour disponible", "zAYm/Z684h": "Aide", "zCIK9K8J4a": "<PERSON><PERSON><PERSON>", "zSP70MVzIo": "<PERSON><PERSON><PERSON><PERSON> le rac<PERSON>ci"}