{"+/cwsayrqk": "Actual Size", "+7sd9hoyZA": "Copy", "/PgA81GVOD": "Edit", "/bRGKhnXQ6": "A new version is available. It will be downloaded and installed automatically.", "0g8/VVdNuN": "Go to claude.ai/settings to configure your profile, team, and more.", "0tZLEYF8mJ": "Developer", "1HUTYwndT2": "Window", "1PfZLi/OV7": "General", "1TJUzU26sO": "Submit <PERSON>…", "25aCMlTDUq": "Automatically start <PERSON> when you log in", "3ML3xT+gEV": "Redo", "3gG1j3kRBX": "Submit <PERSON>...", "3unrKzH4zB": "Copy", "4qP7MjrQfC": "Environment variables", "5DUIVR3fVi": "About...", "6yv8ytK4El": "Check your network connection", "7fdcqxofEs": "Exit", "7gSC+rZzXX": "Quickly open Claude from anywhere", "8YQEOfuaGO": "Select All", "9+afSO9e/t": "Failed to check for updates: {error}", "9uNxNtcrFI": "Install", "CZwl8X2D85": "Advanced options", "CizRPROPWo": "<PERSON>", "D43DeqP+2t": "<PERSON>", "D4DyT6MmPy": "Could not load app settings", "DQTgg21B7g": "Show App", "E9jYTa7AbX": "System Tray", "EfdnINFnIz": "File", "GSG5S0ysrR": "Run on Startup", "HeHYq6bbS2": "<PERSON> can receive information like prompts and attachments from specialized servers using Model Context Protocol.", "I5O68ogAtr": "Get Started", "JVwNvMZjVT": "Paste", "KAo3lt5Hv+": "Paste", "Ko/2Ml7mZG": "Reload This Page", "L32WRR6NOL": "Delete", "L717supPIA": "Settings", "LCWUQ/4Fu6": "View", "NZIwKxgxJ+": "Are you sure you want to remove the MCP server \"{serverKey}\"?", "Nmvo1ufAY5": "Couldn't connect to <PERSON>", "O3rtEd7aMd": "Find", "ODySlGptaj": "Settings…", "PH29MShDiy": "Forward", "PW5U8NgTto": "Open MCP Log File...", "PZtcoAOSsa": "Don't Enable", "PbJ4jR0kv1": "You are running the latest version.", "RTg057HE1D": "Show Dev Tools", "S3MXlbjkax": "What can I help you with today?", "S3k5yXss2r": "Version {version}", "TH+W2Ad73P": "Cut", "UJCjEVPX6Q": "Look Up", "Vvus2ifAny": "Edit Config", "W1pELwt/+a": "<PERSON> runs in the Notification Area", "WBvq3HlPae": "Set shortcut", "WF1HSu0jAC": "Open Logs Folder", "WZe86KSdrM": "Add to dictionary", "WlhIx7DfFO": "OK", "XPIoFTkh3e": "No Update Available", "XZ36+EBE5/": "Zoom Out", "XinCguXCgN": "Learn more", "YTdYCYAf/Z": "Show <PERSON> in the menu bar", "Z9g5m/V9Nq": "Zoom In", "ZJZN1+KyJw": "Settings...", "aNmxuDcWaU": "Error", "aXdFLiVzjd": "Show Main Window", "arbRxbtBkP": "Back", "baGq3gy8z1": "New Conversation", "dKX0bpR+a2": "Quit", "dLyz0Srosd": "Developer", "fEeEFfSz4K": "Zoom In (indie cooler version)", "fFJxOwJRj2": "Undo", "fWDSQQgRO5": "Menu Bar", "iFRmqBsr1N": "About <PERSON>", "ilE9e0uxNN": "Refresh", "j66cdL4EK5": "Open Documentation", "jd5ZNrRMNP": "You're an early explorer, so let us know your feedback.", "k+06oXbIas": "Show Claude in the notifications area", "kYwW0OsI4M": "Claude Desktop is in beta.", "m3GfpKD1WX": "Reload", "mRXjxhS6p4": "Check for Updates…", "ngLpGT7bUJ": "Could not load developer settings", "oQuOiX24pp": "Quit", "pWXxZASpOB": "Help", "pgaCSv2/6H": "Arguments", "q4hs14B00V": "Unknown error", "rNAd+HxSK4": "Open MCP Log File", "rY99UXvTDU": "Copy Image", "rdiPpQVqvY": "Developer mode allows access to developer tools and debugging features. Only enable this if you know what you're doing.", "rwFEudHXey": "There was an error reading or parsing claude_desktop_config.json: {error}", "sNnRQsIEYz": "Find in page", "sZxWXq9BzJ": "Give feedback", "sys7RHphmL": "<PERSON><PERSON><PERSON>", "tWutslc/9Z": "Enable", "u1/hT7oRQY": "Quick Entry Keyboard Shortcut", "uc3dnSo+eo": "File", "urCd4k/cE0": "Command", "vgLHPxjh9O": "Enable Developer Mode", "wS64bVG2CO": "MCP is a protocol that enables secure connections between clients, such as the Claude Desktop app, and local services.", "xJs1jZ8PoA": "<PERSON> runs in the background even when you close the window. Click the <PERSON> icon in the tray to reopen the app, or right-click to quit.", "xKRKzVVy9c": "Configure", "xd436TVDRZ": "There was an error reading or parsing developer_settings.json: {error}", "y9tCbmRzHN": "Enable Developer Mode?", "ytjMRobdyL": "Update Available", "zAYm/Z684h": "Help", "zCIK9K8J4a": "Error", "zSP70MVzIo": "Clear shortcut"}