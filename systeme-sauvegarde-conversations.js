#!/usr/bin/env node

/**
 * SYSTÈME DE SAUVEGARDE ET RESTAURATION DES CONVERSATIONS
 * Sauvegarde automatique + historique complet
 */

const fs = require('fs');
const path = require('path');

class SystemeSauvegardeConversations {
    constructor() {
        this.dossierSauvegarde = './MEMOIRE-REELLE/conversations';
        this.dossierBackup = './MEMOIRE-REELLE/backup';
        this.fichierIndex = path.join(this.dossierSauvegarde, 'index.json');
        
        this.conversations = new Map();
        this.conversationActive = null;
        
        this.initialiserDossiers();
        this.chargerIndex();
        
        console.log('💾 Système de sauvegarde conversations initialisé');
    }

    // Initialiser les dossiers
    initialiserDossiers() {
        if (!fs.existsSync('./MEMOIRE-REELLE')) {
            fs.mkdirSync('./MEMOIRE-REELLE', { recursive: true });
        }
        
        if (!fs.existsSync(this.dossierSauvegarde)) {
            fs.mkdirSync(this.dossierSauvegarde, { recursive: true });
        }
        
        if (!fs.existsSync(this.dossierBackup)) {
            fs.mkdirSync(this.dossierBackup, { recursive: true });
        }
    }

    // Charger l'index des conversations
    chargerIndex() {
        try {
            if (fs.existsSync(this.fichierIndex)) {
                const data = fs.readFileSync(this.fichierIndex, 'utf8');
                const index = JSON.parse(data);
                
                for (const conv of index.conversations) {
                    this.conversations.set(conv.id, conv);
                }
                
                console.log(`📚 ${this.conversations.size} conversations chargées`);
            }
        } catch (error) {
            console.log('⚠️ Erreur chargement index:', error.message);
        }
    }

    // Sauvegarder l'index
    sauvegarderIndex() {
        try {
            const index = {
                version: '2025.1',
                timestamp: new Date().toISOString(),
                conversations: Array.from(this.conversations.values())
            };
            
            fs.writeFileSync(this.fichierIndex, JSON.stringify(index, null, 2));
        } catch (error) {
            console.log('❌ Erreur sauvegarde index:', error.message);
        }
    }

    // Créer une nouvelle conversation
    creerConversation(titre = null) {
        const id = this.genererIdUnique();
        const maintenant = new Date();
        
        const conversation = {
            id: id,
            titre: titre || `Conversation ${maintenant.toLocaleDateString()} ${maintenant.toLocaleTimeString()}`,
            dateCreation: maintenant.toISOString(),
            derniereModification: maintenant.toISOString(),
            messages: [],
            mode: 'normal',
            statistiques: {
                nombreMessages: 0,
                tempsTotal: 0,
                modesUtilises: ['normal']
            }
        };
        
        this.conversations.set(id, conversation);
        this.conversationActive = id;
        this.sauvegarderIndex();
        this.sauvegarderConversation(id);
        
        console.log(`💬 Nouvelle conversation créée: ${id}`);
        return conversation;
    }

    // Ajouter un message à la conversation active
    ajouterMessage(type, contenu, mode = 'normal', metadonnees = {}) {
        if (!this.conversationActive) {
            this.creerConversation();
        }
        
        const conversation = this.conversations.get(this.conversationActive);
        if (!conversation) return null;
        
        const message = {
            id: this.genererIdUnique(),
            type: type, // 'user', 'assistant', 'system'
            contenu: contenu,
            mode: mode,
            timestamp: new Date().toISOString(),
            metadonnees: {
                tempsReponse: metadonnees.tempsReponse || 0,
                qiUtilise: metadonnees.qiUtilise || 226,
                zoneThermique: metadonnees.zoneThermique || 'WORKING',
                ...metadonnees
            }
        };
        
        conversation.messages.push(message);
        conversation.derniereModification = new Date().toISOString();
        conversation.statistiques.nombreMessages++;
        
        if (!conversation.statistiques.modesUtilises.includes(mode)) {
            conversation.statistiques.modesUtilises.push(mode);
        }
        
        this.sauvegarderConversation(this.conversationActive);
        this.sauvegarderIndex();
        
        return message;
    }

    // Sauvegarder une conversation spécifique
    sauvegarderConversation(id) {
        try {
            const conversation = this.conversations.get(id);
            if (!conversation) return false;
            
            const fichier = path.join(this.dossierSauvegarde, `conversation_${id}.json`);
            fs.writeFileSync(fichier, JSON.stringify(conversation, null, 2));
            
            return true;
        } catch (error) {
            console.log('❌ Erreur sauvegarde conversation:', error.message);
            return false;
        }
    }

    // Charger une conversation
    chargerConversation(id) {
        try {
            const fichier = path.join(this.dossierSauvegarde, `conversation_${id}.json`);
            
            if (fs.existsSync(fichier)) {
                const data = fs.readFileSync(fichier, 'utf8');
                const conversation = JSON.parse(data);
                
                this.conversations.set(id, conversation);
                this.conversationActive = id;
                
                return conversation;
            }
            
            return null;
        } catch (error) {
            console.log('❌ Erreur chargement conversation:', error.message);
            return null;
        }
    }

    // Obtenir la conversation active
    obtenirConversationActive() {
        if (!this.conversationActive) return null;
        return this.conversations.get(this.conversationActive);
    }

    // Lister toutes les conversations
    listerConversations() {
        return Array.from(this.conversations.values()).sort((a, b) => 
            new Date(b.derniereModification) - new Date(a.derniereModification)
        );
    }

    // Rechercher dans les conversations
    rechercherConversations(terme) {
        const resultats = [];
        
        for (const conversation of this.conversations.values()) {
            // Recherche dans le titre
            if (conversation.titre.toLowerCase().includes(terme.toLowerCase())) {
                resultats.push({
                    conversation: conversation,
                    type: 'titre',
                    correspondance: conversation.titre
                });
                continue;
            }
            
            // Recherche dans les messages
            for (const message of conversation.messages) {
                if (message.contenu.toLowerCase().includes(terme.toLowerCase())) {
                    resultats.push({
                        conversation: conversation,
                        type: 'message',
                        correspondance: message.contenu.substring(0, 100) + '...',
                        message: message
                    });
                    break;
                }
            }
        }
        
        return resultats;
    }

    // Exporter une conversation
    exporterConversation(id, format = 'json') {
        const conversation = this.conversations.get(id);
        if (!conversation) return null;
        
        const nomFichier = `export_${id}_${Date.now()}`;
        
        switch (format) {
            case 'json':
                const fichierJson = path.join(this.dossierBackup, `${nomFichier}.json`);
                fs.writeFileSync(fichierJson, JSON.stringify(conversation, null, 2));
                return fichierJson;
                
            case 'txt':
                const fichierTxt = path.join(this.dossierBackup, `${nomFichier}.txt`);
                let contenuTxt = `Conversation: ${conversation.titre}\n`;
                contenuTxt += `Créée le: ${new Date(conversation.dateCreation).toLocaleString()}\n`;
                contenuTxt += `Dernière modification: ${new Date(conversation.derniereModification).toLocaleString()}\n`;
                contenuTxt += `Messages: ${conversation.statistiques.nombreMessages}\n\n`;
                
                for (const message of conversation.messages) {
                    const temps = new Date(message.timestamp).toLocaleTimeString();
                    contenuTxt += `[${temps}] ${message.type.toUpperCase()}: ${message.contenu}\n\n`;
                }
                
                fs.writeFileSync(fichierTxt, contenuTxt);
                return fichierTxt;
                
            default:
                return null;
        }
    }

    // Créer une sauvegarde complète
    creerSauvegardeComplete() {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const dossierSauvegarde = path.join(this.dossierBackup, `backup_complet_${timestamp}`);
            
            fs.mkdirSync(dossierSauvegarde, { recursive: true });
            
            // Copier l'index
            fs.copyFileSync(this.fichierIndex, path.join(dossierSauvegarde, 'index.json'));
            
            // Copier toutes les conversations
            let compteur = 0;
            for (const id of this.conversations.keys()) {
                const source = path.join(this.dossierSauvegarde, `conversation_${id}.json`);
                const destination = path.join(dossierSauvegarde, `conversation_${id}.json`);
                
                if (fs.existsSync(source)) {
                    fs.copyFileSync(source, destination);
                    compteur++;
                }
            }
            
            // Créer un fichier de métadonnées
            const metadonnees = {
                version: '2025.1',
                timestamp: new Date().toISOString(),
                nombreConversations: compteur,
                tailleTotale: this.calculerTailleDossier(dossierSauvegarde)
            };
            
            fs.writeFileSync(
                path.join(dossierSauvegarde, 'metadonnees.json'),
                JSON.stringify(metadonnees, null, 2)
            );
            
            console.log(`💾 Sauvegarde complète créée: ${compteur} conversations`);
            return dossierSauvegarde;
            
        } catch (error) {
            console.log('❌ Erreur sauvegarde complète:', error.message);
            return null;
        }
    }

    // Obtenir les statistiques globales
    obtenirStatistiques() {
        let totalMessages = 0;
        let totalConversations = this.conversations.size;
        let modesUtilises = new Set();
        let premiereConversation = null;
        let derniereConversation = null;
        
        for (const conversation of this.conversations.values()) {
            totalMessages += conversation.statistiques.nombreMessages;
            
            for (const mode of conversation.statistiques.modesUtilises) {
                modesUtilises.add(mode);
            }
            
            const dateCreation = new Date(conversation.dateCreation);
            if (!premiereConversation || dateCreation < new Date(premiereConversation.dateCreation)) {
                premiereConversation = conversation;
            }
            
            const dateModification = new Date(conversation.derniereModification);
            if (!derniereConversation || dateModification > new Date(derniereConversation.derniereModification)) {
                derniereConversation = conversation;
            }
        }
        
        return {
            totalConversations,
            totalMessages,
            modesUtilises: Array.from(modesUtilises),
            premiereConversation: premiereConversation?.dateCreation,
            derniereActivite: derniereConversation?.derniereModification,
            tailleStockage: this.calculerTailleDossier(this.dossierSauvegarde)
        };
    }

    // Utilitaires
    genererIdUnique() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    calculerTailleDossier(dossier) {
        try {
            let taille = 0;
            const fichiers = fs.readdirSync(dossier);
            
            for (const fichier of fichiers) {
                const cheminFichier = path.join(dossier, fichier);
                const stats = fs.statSync(cheminFichier);
                taille += stats.size;
            }
            
            return taille;
        } catch (error) {
            return 0;
        }
    }
}

module.exports = SystemeSauvegardeConversations;
