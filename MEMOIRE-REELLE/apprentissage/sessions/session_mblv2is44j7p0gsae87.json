{"id": "mblv2is44j7p0gsae87", "type": "reconnaissance_patterns", "debut": "2025-06-07T06:36:31.252Z", "duree_prevue": 180000, "qi_debut": 172, "objectifs": ["<PERSON><PERSON><PERSON><PERSON> précision", "Augmenter vitesse", "Détecter patterns complexes"], "progres": [{"etape": 2, "type": "pattern_reconnu", "difficulte": 5, "precision": 75.67209050212999}, {"etape": 3, "type": "pattern_reconnu", "difficulte": 1, "precision": 98.10926710191661}, {"etape": 5, "type": "pattern_reconnu", "difficulte": 8, "precision": 72.63162514080865}, {"etape": 8, "type": "pattern_reconnu", "difficulte": 10, "precision": 89.40043028164739}, {"etape": 9, "type": "pattern_reconnu", "difficulte": 5, "precision": 88.23446171078635}, {"etape": 10, "type": "pattern_reconnu", "difficulte": 5, "precision": 71.12737395610608}], "fin": "2025-06-07T06:36:32.263Z", "duree_reelle": 1011, "qi_fin": 174, "gain_qi": 2}