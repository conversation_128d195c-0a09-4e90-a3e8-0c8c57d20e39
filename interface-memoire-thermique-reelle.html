<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LOUNA-AI - Mémoire Thermique Réelle</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            margin: 0;
            padding: 20px;
            color: white;
            min-height: 100vh;
            padding-top: 100px;
        }
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .title {
            font-size: 3.5em;
            font-weight: bold;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3, #54a0ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: thermal 4s ease-in-out infinite;
        }
        @keyframes thermal {
            0%, 100% { filter: hue-rotate(0deg) brightness(1); }
            25% { filter: hue-rotate(90deg) brightness(1.2); }
            50% { filter: hue-rotate(180deg) brightness(1.1); }
            75% { filter: hue-rotate(270deg) brightness(1.3); }
        }
        
        /* STATUT CONNEXION */
        .statut-connexion {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        .statut-ok {
            color: #2ecc71;
            font-size: 1.2em;
            font-weight: bold;
        }
        .statut-erreur {
            color: #e74c3c;
            font-size: 1.2em;
            font-weight: bold;
        }
        
        /* STATISTIQUES GLOBALES */
        .stats-globales {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
        }
        .stats-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #feca57;
            text-align: center;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        .stat-item:hover {
            border-color: #48dbfb;
            transform: translateY(-5px);
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #48dbfb, #2ecc71);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .stat-label {
            font-size: 1em;
            opacity: 0.9;
        }
        
        /* ZONES THERMIQUES */
        .zones-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        .zones-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #ff6b6b;
            text-align: center;
        }
        .zones-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        .zone-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border-left: 5px solid;
            transition: all 0.3s ease;
            position: relative;
        }
        .zone-card:hover {
            transform: translateX(5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        .zone-card.zone1 { border-left-color: #ff6b6b; }
        .zone-card.zone2 { border-left-color: #feca57; }
        .zone-card.zone3 { border-left-color: #48dbfb; }
        .zone-card.zone4 { border-left-color: #ff9ff3; }
        .zone-card.zone5 { border-left-color: #54a0ff; }
        .zone-card.zone6 { border-left-color: #5f27cd; }
        
        .zone-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .zone-name {
            font-size: 1.3em;
            font-weight: bold;
        }
        .zone-temp {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-weight: bold;
        }
        .zone-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        .zone-metric {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 10px;
            text-align: center;
        }
        .zone-metric-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #48dbfb;
        }
        .zone-metric-label {
            font-size: 0.9em;
            opacity: 0.8;
            margin-top: 5px;
        }
        
        /* ACTIONS */
        .actions-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        .actions-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #54a0ff;
            text-align: center;
        }
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .action-btn {
            padding: 15px 20px;
            border: none;
            border-radius: 15px;
            background: linear-gradient(45deg, #48dbfb, #2ecc71);
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1em;
        }
        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        .action-btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        
        /* RECHERCHE SOUVENIRS */
        .recherche-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        .recherche-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #ff9ff3;
            text-align: center;
        }
        .recherche-form {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .recherche-input {
            flex: 1;
            padding: 12px 15px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1em;
        }
        .recherche-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        .recherche-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            background: linear-gradient(45deg, #ff9ff3, #54a0ff);
            color: white;
            font-weight: bold;
            cursor: pointer;
        }
        .resultats-recherche {
            max-height: 300px;
            overflow-y: auto;
        }
        .resultat-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 10px 15px;
            margin-bottom: 8px;
            border-left: 3px solid #ff9ff3;
        }
        
        /* LOGS */
        .logs-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 25px;
            max-height: 300px;
            overflow-y: auto;
        }
        .logs-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #feca57;
        }
        .log-entry {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 10px 15px;
            margin-bottom: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .log-entry.success { border-left: 4px solid #2ecc71; }
        .log-entry.warning { border-left: 4px solid #feca57; }
        .log-entry.error { border-left: 4px solid #ff6b6b; }
        .log-entry.info { border-left: 4px solid #48dbfb; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .pulse { animation: pulse 2s infinite; }
    </style>
</head>
<body>
    <!-- NAVIGATION GLOBALE -->
    <nav style="position: fixed; top: 0; left: 0; right: 0; background: linear-gradient(135deg, rgba(0,0,0,0.9) 0%, rgba(30,30,30,0.95) 100%); backdrop-filter: blur(15px); border-bottom: 2px solid rgba(255,255,255,0.1); z-index: 1000; padding: 10px 0;">
        <div style="max-width: 1400px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center; padding: 0 20px; flex-wrap: wrap;">
            <a href="/" style="font-size: 1.5em; font-weight: bold; background: linear-gradient(45deg, #3498db, #2ecc71, #f39c12, #e74c3c); -webkit-background-clip: text; -webkit-text-fill-color: transparent; text-decoration: none;">🧠 LOUNA-AI</a>

            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                <a href="/" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.1); transition: all 0.3s ease; font-size: 0.9em;">🏠 Accueil</a>
                <a href="/agent" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.1); transition: all 0.3s ease; font-size: 0.9em;">🤖 Agent</a>
                <a href="/apprentissage" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.1); transition: all 0.3s ease; font-size: 0.9em;">📚 Apprentissage</a>
                <a href="/cerveau" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.1); transition: all 0.3s ease; font-size: 0.9em;">🧠 Cerveau</a>
                <a href="/memoire" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: linear-gradient(45deg, #3498db, #2ecc71); transition: all 0.3s ease; font-size: 0.9em;">🔥 Mémoire</a>
                <a href="/pensees" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.1); transition: all 0.3s ease; font-size: 0.9em;">👁️ Pensées</a>
                <a href="/chat" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.1); transition: all 0.3s ease; font-size: 0.9em;">💬 Chat</a>
                <a href="/dashboard" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.1); transition: all 0.3s ease; font-size: 0.9em;">📊 Dashboard</a>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="header">
            <div class="title pulse">🔥 Mémoire Thermique Réelle</div>
            <div class="subtitle">Système de Mémoire Connectée LOUNA-AI</div>
        </div>

        <!-- STATUT CONNEXION -->
        <div class="statut-connexion">
            <div id="statut-connexion" class="statut-ok">
                ✅ Connexion mémoire thermique vérifiée
            </div>
        </div>

        <!-- STATISTIQUES GLOBALES -->
        <div class="stats-globales">
            <div class="stats-title">📊 Statistiques Globales</div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="neurones-totaux">0</div>
                    <div class="stat-label">Neurones Totaux</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="neurones-actifs">0</div>
                    <div class="stat-label">Neurones Actifs</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="pourcentage-activite">0%</div>
                    <div class="stat-label">Activité Globale</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="synapses-totales">0</div>
                    <div class="stat-label">Synapses Totales</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="flux-neural">0</div>
                    <div class="stat-label">Flux Neural</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="temperature-moyenne">0°C</div>
                    <div class="stat-label">Température Moyenne</div>
                </div>
            </div>
        </div>

        <!-- ZONES THERMIQUES -->
        <div class="zones-section">
            <div class="zones-title">🌡️ Zones Thermiques Réelles</div>
            <div class="zones-grid" id="zones-grid">
                <!-- Sera rempli dynamiquement -->
            </div>
        </div>

        <!-- ACTIONS -->
        <div class="actions-section">
            <div class="actions-title">⚡ Actions Mémoire</div>
            <div class="actions-grid">
                <button class="action-btn" onclick="actualiserMemoire()">
                    🔄 Actualiser Données
                </button>
                <button class="action-btn" onclick="creerNeurone()">
                    🧠 Créer Neurone
                </button>
                <button class="action-btn" onclick="ajouterSouvenir()">
                    💾 Ajouter Souvenir
                </button>
                <button class="action-btn" onclick="optimiserZones()">
                    ⚡ Optimiser Zones
                </button>
                <button class="action-btn" onclick="sauvegarderEtat()">
                    💾 Sauvegarder État
                </button>
                <button class="action-btn" onclick="migrerNeurones()">
                    🔄 Migrer Neurones
                </button>
            </div>
        </div>

        <!-- RECHERCHE SOUVENIRS -->
        <div class="recherche-section">
            <div class="recherche-title">🔍 Recherche dans la Mémoire</div>
            <div class="recherche-form">
                <input type="text" class="recherche-input" id="recherche-input" 
                       placeholder="Rechercher dans les souvenirs...">
                <button class="recherche-btn" onclick="rechercherSouvenirs()">
                    🔍 Rechercher
                </button>
            </div>
            <div class="resultats-recherche" id="resultats-recherche">
                <!-- Résultats de recherche -->
            </div>
        </div>

        <!-- LOGS -->
        <div class="logs-section">
            <div class="logs-title">📋 Logs Mémoire Thermique</div>
            <div id="logs-container">
                <div class="log-entry info">
                    [${new Date().toLocaleTimeString()}] Interface mémoire thermique initialisée
                </div>
            </div>
        </div>
    </div>

    <script>
        let updateInterval;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            actualiserMemoire();
            updateInterval = setInterval(actualiserMemoire, 10000); // Mise à jour toutes les 10s
        });

        // Actualiser les données de la mémoire
        async function actualiserMemoire() {
            try {
                const response = await fetch('/api/memoire-thermique/status');
                const data = await response.json();
                
                if (data.success) {
                    updateInterface(data.memoire);
                    addLog('success', 'Données mémoire thermique mises à jour');
                } else {
                    addLog('error', 'Erreur récupération données: ' + data.error);
                    document.getElementById('statut-connexion').textContent = '❌ Erreur connexion mémoire';
                    document.getElementById('statut-connexion').className = 'statut-erreur';
                }
            } catch (error) {
                addLog('error', 'Erreur connexion API: ' + error.message);
                document.getElementById('statut-connexion').textContent = '❌ Connexion impossible';
                document.getElementById('statut-connexion').className = 'statut-erreur';
            }
        }

        // Mettre à jour l'interface
        function updateInterface(data) {
            if (!data || !data.statistiques_globales) return;
            
            // Statistiques globales
            const stats = data.statistiques_globales;
            document.getElementById('neurones-totaux').textContent = stats.neurones_totaux;
            document.getElementById('neurones-actifs').textContent = stats.neurones_actifs;
            document.getElementById('pourcentage-activite').textContent = stats.pourcentage_activite_globale + '%';
            document.getElementById('synapses-totales').textContent = stats.synapses_totales;
            document.getElementById('flux-neural').textContent = stats.flux_neural_global;
            document.getElementById('temperature-moyenne').textContent = stats.temperature_moyenne;
            
            // Zones thermiques
            if (data.zones) {
                updateZones(data.zones);
            }
            
            // Statut connexion
            if (data.connexion_status && data.connexion_status.structure_ok) {
                document.getElementById('statut-connexion').textContent = 
                    `✅ Mémoire connectée - ${data.connexion_status.zones_actives}/${data.connexion_status.zones_detectees} zones actives`;
                document.getElementById('statut-connexion').className = 'statut-ok';
            }
        }

        // Mettre à jour les zones
        function updateZones(zones) {
            const container = document.getElementById('zones-grid');
            container.innerHTML = '';

            zones.forEach((zone, index) => {
                const card = document.createElement('div');
                card.className = `zone-card ${zone.id}`;
                card.innerHTML = `
                    <div class="zone-header">
                        <div class="zone-name">🌡️ ${zone.nom}</div>
                        <div class="zone-temp">${zone.temperature}°C</div>
                    </div>
                    <div class="zone-metrics">
                        <div class="zone-metric">
                            <div class="zone-metric-value">${zone.neurones_totaux.toLocaleString()}</div>
                            <div class="zone-metric-label">Neurones Totaux</div>
                        </div>
                        <div class="zone-metric">
                            <div class="zone-metric-value">${zone.neurones_actifs.toLocaleString()}</div>
                            <div class="zone-metric-label">Neurones Actifs</div>
                        </div>
                        <div class="zone-metric">
                            <div class="zone-metric-value">${zone.pourcentage_activite}%</div>
                            <div class="zone-metric-label">Activité</div>
                        </div>
                        <div class="zone-metric">
                            <div class="zone-metric-value">${zone.synapses_actives.toLocaleString()}</div>
                            <div class="zone-metric-label">Synapses Actives</div>
                        </div>
                        <div class="zone-metric">
                            <div class="zone-metric-value">${zone.flux_neural.toLocaleString()}</div>
                            <div class="zone-metric-label">Flux Neural</div>
                        </div>
                        <div class="zone-metric">
                            <div class="zone-metric-value">${zone.synapses_estimees.toLocaleString()}</div>
                            <div class="zone-metric-label">Synapses Totales</div>
                        </div>
                    </div>
                `;
                container.appendChild(card);
            });
        }

        // Créer un neurone
        async function creerNeurone() {
            try {
                const response = await fetch('/api/memoire-thermique/creer-neurone', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ zone: 'zone1', type: 'general' })
                });
                
                const data = await response.json();
                if (data.success) {
                    addLog('success', `Neurone créé: ${data.neurone_id}`);
                    actualiserMemoire();
                } else {
                    addLog('error', 'Erreur création neurone: ' + data.error);
                }
            } catch (error) {
                addLog('error', 'Erreur création neurone: ' + error.message);
            }
        }

        // Ajouter un souvenir
        async function ajouterSouvenir() {
            const contenu = prompt('Contenu du souvenir:');
            if (!contenu) return;
            
            try {
                const response = await fetch('/api/memoire-thermique/ajouter-souvenir', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ contenu: contenu, type: 'manuel', zone: 'zone1' })
                });
                
                const data = await response.json();
                if (data.success) {
                    addLog('success', `Souvenir ajouté: ${data.souvenir_id}`);
                    actualiserMemoire();
                } else {
                    addLog('error', 'Erreur ajout souvenir: ' + data.error);
                }
            } catch (error) {
                addLog('error', 'Erreur ajout souvenir: ' + error.message);
            }
        }

        // Rechercher souvenirs
        async function rechercherSouvenirs() {
            const terme = document.getElementById('recherche-input').value;
            if (!terme) return;
            
            try {
                const response = await fetch(`/api/memoire-thermique/rechercher?terme=${encodeURIComponent(terme)}`);
                const data = await response.json();
                
                if (data.success) {
                    afficherResultatsRecherche(data.resultats);
                    addLog('info', `Recherche: ${data.resultats.length} résultats pour "${terme}"`);
                } else {
                    addLog('error', 'Erreur recherche: ' + data.error);
                }
            } catch (error) {
                addLog('error', 'Erreur recherche: ' + error.message);
            }
        }

        // Afficher résultats de recherche
        function afficherResultatsRecherche(resultats) {
            const container = document.getElementById('resultats-recherche');
            container.innerHTML = '';
            
            if (resultats.length === 0) {
                container.innerHTML = '<div class="resultat-item">Aucun résultat trouvé</div>';
                return;
            }
            
            resultats.forEach(resultat => {
                const item = document.createElement('div');
                item.className = 'resultat-item';
                item.innerHTML = `
                    <strong>Zone ${resultat.zone_nom} (${resultat.zone_temperature}°C)</strong><br>
                    ${resultat.contenu}<br>
                    <small>Type: ${resultat.type} | Force: ${resultat.force} | ${new Date(resultat.timestamp).toLocaleString()}</small>
                `;
                container.appendChild(item);
            });
        }

        // Actions supplémentaires
        function optimiserZones() {
            addLog('info', 'Optimisation des zones thermiques...');
            // TODO: Implémenter optimisation
        }

        function sauvegarderEtat() {
            addLog('info', 'Sauvegarde de l\'état en cours...');
            actualiserMemoire();
        }

        function migrerNeurones() {
            addLog('info', 'Migration automatique des neurones...');
            // TODO: Implémenter migration
        }

        // Ajouter un log
        function addLog(type, message) {
            const container = document.getElementById('logs-container');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            
            container.insertBefore(entry, container.firstChild);
            
            // Limiter à 50 logs
            while (container.children.length > 50) {
                container.removeChild(container.lastChild);
            }
        }

        // Recherche en temps réel
        document.getElementById('recherche-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                rechercherSouvenirs();
            }
        });
    </script>
</body>
</html>
