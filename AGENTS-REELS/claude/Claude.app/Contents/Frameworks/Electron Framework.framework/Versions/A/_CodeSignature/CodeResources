<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Info.plist</key>
		<data>
		1bIEIwO4Dq7iKNAUMyJ5HuFBT/E=
		</data>
		<key>Resources/MainMenu.nib</key>
		<data>
		8vZwWbxqzsKMQ1nd2hqwWHK4HT8=
		</data>
		<key>Resources/af.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			YKAhEj9shb+Yq2kzs0+n7ODiZvU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/am.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			VNq8eDqiH3O6AWiulHanuMoIlrk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			ODTTT4jRGuX728UQhBbVauMAAu4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bg.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			uT2M9JZRxuj5NueLjKs/a/3SCKc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bn.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			4IL2oHLkjhBzGpSz13E/knnTsjI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			3v4tovsQVsAcValmFZ8okPlxFOA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/chrome_100_percent.pak</key>
		<data>
		Pxmnpwa3c0UHr9LsTaf9jSvdgI8=
		</data>
		<key>Resources/chrome_200_percent.pak</key>
		<data>
		vX1pZOsCozcK3DmBPMz+bo95tdI=
		</data>
		<key>Resources/cs.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			5BjgmhhFuwsJpxUHezJzV16gr4M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			bUsj8ipt02sc4s8uv/dWfA/FFkg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			dJtv8gCBsufWSyvil9u4nzG6New=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			wniTYFKzBw3wsuwMUFGK89NeGmE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			hsGhJntOrbTNXzNArGw/u/J8R7s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en_GB.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			u1EwxPRAYJ9xpJOPR8dsEiVU5eU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Jt2e5tNbrq+XmYgBqRxJxMF5clw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es_419.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			j+UJ6619cW80PT4gTCA99Qei08o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/et.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			T16OO0+sIobG1Evv8bwGfLrZxpM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fa.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			SNLXrT7BOhRmwawv/IjYO7x5QdA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			5fefL/tLm5Q7s66MGEOI+vVMDaA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fil.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			URJ48QxLC03frnlzAJWZMxrz0g4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			EyfahnZ9dW5fNR5jiWnlCxuVd8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/gu.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			rBndsyYNbqROzsLcdP01seFHr3g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			syEqDmQWBzqSZ3FfVGu7kgItCmc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hi.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			9RQeJZj9+qhUCqH3P97vNQ3Pnn4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			P4VLM3VSXJeO1pJFVPwF0NWeiqA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			rUBh1f/xn+Jh+HeG4GvtlekKT/0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/icudtl.dat</key>
		<data>
		dE1m2W0xeXaoMjOt7A9QykUv6C0=
		</data>
		<key>Resources/id.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			SDKNEF1Pv4p+p8u12rTpk82BoO4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			brtV9SqJHzKiR09rIvnuDuHsN14=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			MBSXZP2K4f0MIUWVMJk2R0zCi6Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/kn.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			zlZQH6GyPbZy394wIGGrX3I7gBY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			rDbORgAOi1X3QiAPXwgtu86uuZ4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lt.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			8UdvYvoYJqkXBJuHXyeNFJW4yJw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lv.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			GP1zCrN1S8hAUOm4PX6T20YkdN0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ml.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			lsWYJCfb9czcAg+c/JfQjDoz0Qk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/mr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			v+geHsyxiOgG2h4FlaIUQBCprNw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ms.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			/FYRtQjBvQpBGiFXsxJZ2YuNSaI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			sPBdcWLU4NH0w3ifBuuVf5285ME=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Z1itzQZylZ6Rg1GHMmPC0oTQowg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			dXlbGrgeklpC7hmERtbuvgdFRgI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_BR.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			LNbRxzlZlPNfx3u7hYxWRvmQ73c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			g+ttwc+6W/p+z9XrVf52yKVfXsU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/resources.pak</key>
		<data>
		ZRAAyvo/C6MYmYHBmKLZVHgzIF0=
		</data>
		<key>Resources/ro.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			bG7mGsPqlSrLxSq9sy0LOJIbaO4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			pjeKcDAXNpBZyaVWfrK3/ih0kFU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			mITxmstM8JDDOuMeq45iir7uc+c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			mwUG5jy5cm5YeijJ2g7pVZiod+A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			X1RK5ieBsUhttFnpksi1UILy0Lc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			75Pt5iGL2nJ4aJkR2qUy5WkTGL4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sw.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			rVLylJoGjbfSnG/LWVaJE+EYFUM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ta.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			olRTHOI8RLXM/YOi4u4vJi7WSe0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/te.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			s0J02g7HpoInHvp+GYHX5cXX/KM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			2xTBx8YdDXyWZP6/fRHME9GTykc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			CtHnWiR+qGRFZMHjxOfUvZB1uWo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			AR++NTNAi5RCaEzkfvh5LAtCagg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ur.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			AhphO5SBHDfpZ2nrJhVknsXiTdc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/v8_context_snapshot.arm64.bin</key>
		<data>
		FB1ojwJa1fg/kBLW+rbL1/Vf8bo=
		</data>
		<key>Resources/v8_context_snapshot.x86_64.bin</key>
		<data>
		ENIIgvHSSRtxpbGiQ73xoCzAEMs=
		</data>
		<key>Resources/vi.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			D0FkwcMi0+me01KziEUx8CtsWrg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			asDGZbTs2xA+esfjD1wxbvC28MM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Sg5o0iPa9laxw2TvYI5DaJ2/1sE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Helpers/chrome_crashpad_handler</key>
		<dict>
			<key>cdhash</key>
			<data>
			9T2cF3Pv4+1fIeRO9JICIUCHa78=
			</data>
			<key>requirement</key>
			<string>identifier "chrome_crashpad_handler" and anchor apple generic and certificate 1[field.1.2.840.113635.*********] /* exists */ and certificate leaf[field.1.2.840.113635.**********] /* exists */ and certificate leaf[subject.OU] = Q6L2SF6YDW</string>
		</dict>
		<key>Libraries/libEGL.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			eXjxAdyXpBYUjlXgsQTSNF6h3RfueC6KLJoNHCYxPtw=
			</data>
		</dict>
		<key>Libraries/libGLESv2.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			eed/zTqOnzARVu3MDARzfl3MTgTqrvIxYhIyXvJwGVY=
			</data>
		</dict>
		<key>Libraries/libffmpeg.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			4XdxuiVWyQXnmUlIbirMWmnsu2HN+jNfLCWu2IS/DN4=
			</data>
		</dict>
		<key>Libraries/libvk_swiftshader.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			q5Aa88qs9nqptmFVA4k8xNsXe1Y3qfc8r5HYDXDy89A=
			</data>
		</dict>
		<key>Libraries/vk_swiftshader_icd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1xfZFeMefCeUi4CzarNOLYl4iBFMXH0K+DX5PrU+WPU=
			</data>
		</dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			9QsyhplutBrwTkBzPmaFpn7aEjVQPKNhDuYCaDZ4+lk=
			</data>
		</dict>
		<key>Resources/MainMenu.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			SyFo3i1tmgmyD6qiMTnV8l9zsllWcd4lhy0ZG2k52Bk=
			</data>
		</dict>
		<key>Resources/af.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			vfSWJ6wPmKNA/vENjBEu6Ks+d7Enb8Lw1KmewTfa2Qk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/am.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			vAvI8hDbqDKJ59d5NVkNreM9z3nkpRgD5bD1z6+e2Po=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			ekV5CCWVVQF9/knW6axsFEjN8s2MLK5kh3sOz3B8hmk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bg.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			rKFHB4HlJXGQboZ43ZBpeg8xmu462PyQcmc4ZAa8jGE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bn.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			7CPO3a47hoku1FsCC/nwfLa7OoPX9TTGTkr/C4BHXzA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			bh6Mf8ietxpPU06jTByKDCcZ+Tq8qu8ozcBzsIYLEzM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/chrome_100_percent.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			KWjGPj3JwnbmGUOzqUwrdDcGEJ41RatENQieKwyyeU4=
			</data>
		</dict>
		<key>Resources/chrome_200_percent.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			JFBuEv+hx4PTdmBozZRUnK+D1fVTjb1JfgBn+rPO59g=
			</data>
		</dict>
		<key>Resources/cs.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			EJKi0VfSLYCY8O3ijaqDFHX62VlwR7cFM5CuSI3vynk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			9xMWY+7Tr4dVKepMc7Jekv9r+gs7PVRywQZwN7oAq3w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			AzShoDemeUgwo35XR7Zx1cobVf4OkWVklYWgCh+l/Kg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			FkIQOvwWVEzty3ba0J1BtIOmsyZXH6/lTlkpShYViGM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			cnWLH4yJd3jwG8RSrROBRAB584MgrDnz27VUsr83Xpg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en_GB.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			VM/s1mIkbvNbgbTIeghqAbdjKR8rf3DnWldGyzag1nQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			AGXs0AvWYPn7pAIxok606LgZw3IzWJn7VRAlPERsByg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es_419.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			QyId2qSTkWEXRJ6S78DyCCIjtbZO/qXV3MaFYgFpKd8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/et.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Xsr5cxdn0XN4ic3d/IPJ9YXpfnusGAh7ahBGdA/Uk3U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fa.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			futdG1FDT8AoiV+0EpTRYnIX1rcu3R8VAi/IlzneCPI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			nQFXDyyXUrpK5k7cbn8YdT8x+xARTVK4Fi8jTSRC2oE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fil.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			QLZYhHNSLebioQolk8OKyzigmW2m2BI+YeR2EVOTnDs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			W8uK7aGFtiBOOiOHUt+/AWjqF+JMwnFoPf64pcNSe8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/gu.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Bq2jIf33uAszJ5DIzde5mM94xB+93RDnH2FyVisc6fU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Bvh1w1RWy8CTRB5FEtCfSWmTZNphnUenm7wjJCmthV4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hi.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			2Ikam4HfL5vUv7USKi5N+tyZL4Lbv1hk3VdF5vPO9jo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			k35JEQSsQQG8sAwxc0gyajdaWnD9/ZQlpMQ9Fi8R8DE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			0ZaCsbDipceFxc6Di5ScQjuXY648oVvYAEkY5izmzgs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/icudtl.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			kZL1RTZl/P3hgPEoPy+8zEd/TaoAgeOQPsAtkkJyGGA=
			</data>
		</dict>
		<key>Resources/id.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			TdYK7R626vj3DYKsGxEpsWOWu0BJzjr7FsgZggAUu5w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			B26tQuy6vuHE4kv4H0U/GUGaYeBFFR0Bc4R7edTFVeE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			j2hcwO1MjDPB1cY2BdALbQ+J8lARteEcdtrJzy2RYCY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/kn.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			FVeoQI18LGC4A/4tAodl/tfzI7TuZrS7Csn3nolRo48=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			kPsDB2ABlsKZrOQ+0j/rWrKBgutTTI6t2v9PmuwO+/w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lt.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			4NZdjItEFxbeMppiXURi+HFCIF56y7NRncZPOS9Z7W4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lv.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			LUs+lkVp/4kyXYwhfA9AzhPsZHvbfs50lSz6VPbnkeQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ml.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			KtwYEB49dKuJEvEkt14rXlKwU8VTyzz7bJZSmlG8JkI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/mr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			onhqZW65nYsp7kUFwwMSQA0brluogc2HwXXOcFiS0eM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ms.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			WbVDlIdkoDp0wK738V9uY6Xffe8ktP068EM30SiWZS0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			R24ZBwlBFMZzGrM6Zr+mtNfdyI9xwt4z0UmfSSapjvE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			1/xuro9veP/NOmwvDtrmqhBTnRSzuR2ffCpFPdIkjYM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			nMYH3dhW7e8VbbHqkEbnSPmMxqcahWsl8xkchvY8NEc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_BR.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			8yhtMZwtKlvLHVswlH+OBJfc8qqLObCpDpZ1qqh1ZKw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			mtxvUZpAhhJZ1aaMAWKbsKD1Q9RiLcDuKHkKLxfiMrI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/resources.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Wel9SO355Sb6/Oz/L1c18hOlnM+96boJCZnQlE5cK+c=
			</data>
		</dict>
		<key>Resources/ro.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			oxTjbgIY1/KbWcF4uZME3j+bLj9Q3wMalWu/zAUaF74=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			sS2ThauBNptwzxk+i6evSB/2q6VKPs2YKoW8EoSA218=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			AD1FcpE14QlgskX7cG8OkeTYAtNfO58uS7r5vT8omts=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			0F87l2aMAYypn6ACgypIBzhVNqXQ39QZ8enj5fI4DK0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			JW4Qa74DyZqvfuu8/ldMNPOTo2sa6gV4tXq3pLS5uNs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			n7HEx/SqxgUqpvZ6qjG+iW2Lfm27jhNhDVIGGgaoJ28=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sw.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Jq3z7M+4T4KYm+Y02BQhH1lpeIij6ezGURsD/YIY1xE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ta.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			eJ35iCx/wdBuzif1OzFuG68/XNFxbplrEvLp5xSwNQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/te.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			yf4Zw4cZUZ+foFEhfYBrXbCq0U/oiW3LL13pqpDhzro=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			/eQ6GOhKBB2QcA+M3JuayNF6Cfk+1IG5iFw4MY2dxCQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			2/vuTRULuZvC3xfnpheRKuEcXTERE9o9kZs8ks8zqgs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			/Ut8kGTAtopck0+LcYOsYOuCzLL/2oZseWzco5crFmg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ur.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			54ivl7tlzzeTwGhpxyLjz+1TwZtXT82O6JvfzBMGp3Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/v8_context_snapshot.arm64.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			giH2EYEluzg3Z922jcYtPvA+JyygKyN+iRLuvatwdpk=
			</data>
		</dict>
		<key>Resources/v8_context_snapshot.x86_64.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			8Ju0WZrj698Mm8Tc+IkGn9YnGQo7Rs+2NWKJeQblG1w=
			</data>
		</dict>
		<key>Resources/vi.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			zZSRvAJNjh3gpxrGgZgSNY7QTPjP++zb651ocyevKUM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			LCO/26PQyh0gmv09IFB3wg8PJdnclzvZF8Zh83mLfsw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			19DaE/5nkNKVlH/rN3Zp15o9iDW8Kainwa+lkr+/01M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
