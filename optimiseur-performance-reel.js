#!/usr/bin/env node

/**
 * OPTIMISEUR PERFORMANCE RÉEL - LOUNA-AI
 * Optimise automatiquement les performances du système
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class OptimiseurPerformanceReel {
    constructor() {
        this.metriques = {
            cpu: 0,
            memoire: 0,
            qi_evolution: 0,
            reponse_temps: 0,
            erreurs: 0
        };
        
        this.optimisations = {
            cache_actif: false,
            compression_memoire: false,
            parallele_actif: false,
            gpu_boost: false
        };
        
        this.seuils = {
            cpu_max: 80,
            memoire_max: 85,
            temps_reponse_max: 3000,
            erreurs_max: 5
        };
        
        this.demarrerOptimisation();
    }

    // MESURE PERFORMANCES RÉELLES
    mesurer() {
        try {
            // CPU Usage
            const cpuInfo = execSync('top -l 1 -n 0 | grep "CPU usage"', { encoding: 'utf8' });
            this.metriques.cpu = this.extraireCPU(cpuInfo);
            
            // Mémoire
            const memInfo = execSync('vm_stat', { encoding: 'utf8' });
            this.metriques.memoire = this.extraireMemoire(memInfo);
            
            // Temps de réponse Ollama
            const startTime = Date.now();
            try {
                execSync('curl -s http://localhost:11434/api/tags', { timeout: 5000 });
                this.metriques.reponse_temps = Date.now() - startTime;
            } catch (error) {
                this.metriques.reponse_temps = 5000;
            }
            
            console.log(`📊 CPU: ${this.metriques.cpu}% | RAM: ${this.metriques.memoire}% | Réponse: ${this.metriques.reponse_temps}ms`);
            
        } catch (error) {
            console.log('⚠️ Erreur mesure:', error.message);
        }
    }

    extraireCPU(cpuInfo) {
        const match = cpuInfo.match(/(\d+\.\d+)%\s+user/);
        return match ? parseFloat(match[1]) : 0;
    }

    extraireMemoire(memInfo) {
        const lines = memInfo.split('\n');
        let total = 0, used = 0;
        
        for (const line of lines) {
            if (line.includes('Pages free:')) {
                const free = parseInt(line.match(/\d+/)[0]) * 4096;
                total += free;
            }
            if (line.includes('Pages active:')) {
                const active = parseInt(line.match(/\d+/)[0]) * 4096;
                used += active;
                total += active;
            }
        }
        
        return total > 0 ? Math.round((used / total) * 100) : 0;
    }

    // OPTIMISATIONS AUTOMATIQUES
    optimiserAutomatiquement() {
        console.log('⚡ Optimisation automatique...');
        
        // CPU trop élevé
        if (this.metriques.cpu > this.seuils.cpu_max) {
            this.activerCompressionMemoire();
            this.reduireParallelisme();
        }
        
        // Mémoire trop élevée
        if (this.metriques.memoire > this.seuils.memoire_max) {
            this.viderCache();
            this.optimiserGC();
        }
        
        // Temps de réponse trop lent
        if (this.metriques.reponse_temps > this.seuils.temps_reponse_max) {
            this.activerGPUBoost();
            this.augmenterParallelisme();
        }
        
        // Performance bonne - maintenir
        if (this.metriques.cpu < 50 && this.metriques.memoire < 60) {
            this.activerCacheIntelligent();
        }
    }

    activerCompressionMemoire() {
        if (!this.optimisations.compression_memoire) {
            console.log('🗜️ Activation compression mémoire...');
            process.env.NODE_OPTIONS = '--max-old-space-size=4096 --optimize-for-size';
            this.optimisations.compression_memoire = true;
        }
    }

    activerGPUBoost() {
        if (!this.optimisations.gpu_boost) {
            console.log('🚀 Activation GPU boost...');
            process.env.OLLAMA_GPU_LAYERS = '999';
            process.env.OLLAMA_METAL = '1';
            process.env.OLLAMA_FLASH_ATTENTION = '1';
            this.optimisations.gpu_boost = true;
        }
    }

    activerCacheIntelligent() {
        if (!this.optimisations.cache_actif) {
            console.log('💾 Activation cache intelligent...');
            process.env.OLLAMA_KEEP_ALIVE = '24h';
            process.env.OLLAMA_MAX_LOADED_MODELS = '3';
            this.optimisations.cache_actif = true;
        }
    }

    augmenterParallelisme() {
        if (!this.optimisations.parallele_actif) {
            console.log('⚡ Augmentation parallélisme...');
            process.env.OLLAMA_NUM_PARALLEL = '16';
            process.env.OLLAMA_NUM_THREAD = '8';
            this.optimisations.parallele_actif = true;
        }
    }

    reduireParallelisme() {
        if (this.optimisations.parallele_actif) {
            console.log('🔄 Réduction parallélisme...');
            process.env.OLLAMA_NUM_PARALLEL = '4';
            process.env.OLLAMA_NUM_THREAD = '4';
            this.optimisations.parallele_actif = false;
        }
    }

    viderCache() {
        console.log('🧹 Vidage cache...');
        try {
            if (global.gc) {
                global.gc();
            }
            execSync('purge', { timeout: 5000 });
        } catch (error) {
            // Erreur silencieuse
        }
    }

    optimiserGC() {
        console.log('♻️ Optimisation Garbage Collector...');
        process.env.NODE_OPTIONS = '--expose-gc --max-old-space-size=2048';
    }

    // MONITORING CONTINU
    demarrerOptimisation() {
        console.log('🎯 ========================================');
        console.log('⚡ OPTIMISEUR PERFORMANCE RÉEL DÉMARRÉ');
        console.log('🎯 ========================================');
        
        // Mesure initiale
        this.mesurer();
        
        // Optimisation toutes les 15 secondes
        setInterval(() => {
            this.mesurer();
            this.optimiserAutomatiquement();
            this.genererRapport();
        }, 15000);
        
        // Rapport détaillé toutes les minutes
        setInterval(() => {
            this.sauvegarderMetriques();
        }, 60000);
    }

    genererRapport() {
        const status = this.determinerStatus();
        console.log(`🎯 Status: ${status} | Optimisations: ${Object.values(this.optimisations).filter(Boolean).length}/4`);
    }

    determinerStatus() {
        if (this.metriques.cpu > 80 || this.metriques.memoire > 85) return '🔴 CRITIQUE';
        if (this.metriques.cpu > 60 || this.metriques.memoire > 70) return '🟡 ATTENTION';
        return '🟢 OPTIMAL';
    }

    sauvegarderMetriques() {
        const rapport = {
            timestamp: new Date().toISOString(),
            metriques: { ...this.metriques },
            optimisations: { ...this.optimisations },
            status: this.determinerStatus()
        };
        
        try {
            const fichier = path.join(__dirname, 'MEMOIRE-REELLE', 'performance-metrics.json');
            const dir = path.dirname(fichier);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
            
            let historique = [];
            if (fs.existsSync(fichier)) {
                historique = JSON.parse(fs.readFileSync(fichier, 'utf8'));
            }
            
            historique.push(rapport);
            
            // Garder seulement les 100 dernières mesures
            if (historique.length > 100) {
                historique = historique.slice(-100);
            }
            
            fs.writeFileSync(fichier, JSON.stringify(historique, null, 2));
        } catch (error) {
            // Erreur silencieuse
        }
    }

    // API pour obtenir les métriques
    obtenirMetriques() {
        return {
            metriques: this.metriques,
            optimisations: this.optimisations,
            status: this.determinerStatus()
        };
    }
}

// Démarrer l'optimiseur
const optimiseur = new OptimiseurPerformanceReel();

// Export pour utilisation dans d'autres modules
module.exports = optimiseur;

// Si lancé directement
if (require.main === module) {
    console.log('🚀 Optimiseur performance en cours...');
    console.log('🛑 Appuyez sur Ctrl+C pour arrêter');
    
    process.on('SIGINT', () => {
        console.log('\n🛑 Arrêt optimiseur performance');
        process.exit(0);
    });
}
