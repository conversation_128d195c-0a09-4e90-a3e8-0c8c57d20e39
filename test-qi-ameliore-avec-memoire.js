#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Système de mémoire thermique intégré
class MemoireThermique {
    constructor() {
        this.zones = {
            zone1_70C: new Map(), // Mémoire immédiate
            zone2_60C: new Map(), // Court terme
            zone3_50C: new Map(), // Travail
            zone4_40C: new Map(), // Moyen terme
            zone5_30C: new Map(), // Long terme
            zone6_20C: new Map()  // Permanente
        };
        
        this.apprentissages = new Map(); // Stockage des corrections
        this.patterns = new Map(); // Patterns détectés
    }
    
    add(cle, donnees, importance = 0.5) {
        const temperature = 20 + (importance * 50);
        const zone = this.determinerZone(temperature);
        
        const element = {
            data: donnees,
            temperature: temperature,
            timestamp: Date.now(),
            importance: importance
        };
        
        this.zones[zone].set(cle, element);
        return element;
    }
    
    determinerZone(temperature) {
        if (temperature >= 65) return 'zone1_70C';
        if (temperature >= 55) return 'zone2_60C';
        if (temperature >= 45) return 'zone3_50C';
        if (temperature >= 35) return 'zone4_40C';
        if (temperature >= 25) return 'zone5_30C';
        return 'zone6_20C';
    }
    
    get(cle) {
        for (const zone of Object.values(this.zones)) {
            if (zone.has(cle)) {
                const element = zone.get(cle);
                element.temperature += 5; // Réchauffement par accès
                return element.data;
            }
        }
        return null;
    }
    
    apprendreCorrection(question, reponseIncorrecte, reponseCorrecte, explication) {
        const apprentissage = {
            question: question,
            erreur: reponseIncorrecte,
            correction: reponseCorrecte,
            explication: explication,
            timestamp: Date.now()
        };
        
        this.apprentissages.set(question, apprentissage);
        this.add(`correction_${Date.now()}`, apprentissage, 0.9);
        
        console.log(`🧠 Apprentissage stocké: ${question.substring(0, 50)}...`);
    }
    
    obtenirContexte(question) {
        // Rechercher des apprentissages similaires
        let contexte = "Contexte d'apprentissage:\n";
        
        for (const [cle, apprentissage] of this.apprentissages) {
            if (this.questionsSimilaires(question, cle)) {
                contexte += `- Erreur précédente: ${apprentissage.erreur}\n`;
                contexte += `- Correction: ${apprentissage.correction}\n`;
                contexte += `- Explication: ${apprentissage.explication}\n\n`;
            }
        }
        
        return contexte;
    }
    
    questionsSimilaires(q1, q2) {
        const mots1 = q1.toLowerCase().split(/\s+/);
        const mots2 = q2.toLowerCase().split(/\s+/);
        
        let motsCommuns = 0;
        for (const mot of mots1) {
            if (mots2.includes(mot) && mot.length > 3) {
                motsCommuns++;
            }
        }
        
        return motsCommuns >= 2;
    }
}

class TestQIAmelioreAvecMemoire {
    constructor() {
        console.log('🧠 TEST QI AMÉLIORÉ AVEC MÉMOIRE THERMIQUE');
        console.log('==========================================');
        
        // Initialiser la mémoire thermique
        this.memoire = new MemoireThermique();
        
        // Configuration Ollama
        this.ollamaPath = '/usr/local/bin/ollama';
        this.modelName = 'deepseek-r1:7b';
        this.modelBackup = 'llama3.2:1b';
        
        // Pré-charger les corrections des erreurs précédentes
        this.prechargerCorrections();
        
        // Questions QI améliorées avec corrections
        this.questionsQI = [
            // LOGIQUE - Corrigées
            {
                type: 'logique',
                question: "Complétez la séquence: 2, 6, 12, 20, 30, ?",
                reponse_correcte: "42",
                explication: "Différences: +4, +6, +8, +10, +12 → 30+12=42",
                points: 5,
                correction: "Analysez les différences entre nombres consécutifs: 6-2=4, 12-6=6, 20-12=8, 30-20=10. La suite des différences est +4,+6,+8,+10, donc la prochaine différence est +12."
            },
            {
                type: 'logique',
                question: "Quel nombre continue la série de Fibonacci: 1, 1, 2, 3, 5, 8, ?",
                reponse_correcte: "13",
                explication: "Suite de Fibonacci: chaque nombre = somme des deux précédents (5+8=13)",
                points: 5,
                correction: "Dans la suite de Fibonacci, chaque nombre est la somme des deux précédents: 1+1=2, 1+2=3, 2+3=5, 3+5=8, 5+8=13."
            },
            {
                type: 'logique',
                question: "Si 3 chats attrapent 3 souris en 3 minutes, combien de chats faut-il pour attraper 100 souris en 100 minutes?",
                reponse_correcte: "3",
                explication: "Ratio constant: 1 chat attrape 1 souris par minute, donc 3 chats attrapent 100 souris en 100 minutes",
                points: 5,
                correction: "Calculez le ratio: 3 chats / 3 souris / 3 minutes = 1 chat attrape 1 souris en 1 minute. Pour 100 souris en 100 minutes: 100 souris ÷ (100 minutes ÷ 1 minute par souris) = 3 chats."
            },
            
            // MATHÉMATIQUES - Corrigées
            {
                type: 'mathematique',
                question: "Un triangle rectangle a des côtés de 3, 4 et 5. Quelle est son aire?",
                reponse_correcte: "6",
                explication: "Triangle rectangle: aire = (base × hauteur) ÷ 2 = (3×4)÷2 = 6",
                points: 5,
                correction: "Pour un triangle rectangle, l'aire = (côté1 × côté2) ÷ 2, où côté1 et côté2 sont les côtés perpendiculaires (pas l'hypoténuse). Ici: (3×4)÷2 = 6."
            },
            {
                type: 'mathematique',
                question: "Quelle est la probabilité d'obtenir exactement 2 faces en lançant 3 pièces?",
                reponse_correcte: "3/8",
                explication: "3 combinaisons favorables (FFP, FPF, PFF) sur 8 possibles = 3/8",
                points: 5,
                correction: "Énumérez toutes les combinaisons: FFF, FFP, FPF, FPP, PFF, PFP, PPF, PPP. Celles avec exactement 2 faces: FFP, FPF, PFF = 3 sur 8 = 3/8."
            },
            
            // VERBAL - Corrigées
            {
                type: 'verbal',
                question: "Quel mot est à 'LIVRE' ce que 'ÉCOUTER' est à 'MUSIQUE'?",
                reponse_correcte: "lire",
                explication: "Relation action-objet: on lit un livre, on écoute la musique",
                points: 5,
                correction: "Cherchez la relation: ÉCOUTER est l'action qu'on fait avec la MUSIQUE. Quelle action fait-on avec un LIVRE? On le LIT."
            },
            {
                type: 'verbal',
                question: "Trouvez l'intrus: Chien, Chat, Oiseau, Poisson, Automobile",
                reponse_correcte: "automobile",
                explication: "Seul objet non-vivant parmi les êtres vivants",
                points: 5,
                correction: "Classifiez chaque élément: Chien=animal, Chat=animal, Oiseau=animal, Poisson=animal, Automobile=objet. L'intrus est l'automobile car c'est le seul objet inanimé."
            },
            
            // SPATIAL - Corrigées
            {
                type: 'spatial',
                question: "Un cube a 6 faces. Combien d'arêtes a-t-il?",
                reponse_correcte: "12",
                explication: "Un cube a 12 arêtes: 4 arêtes par face × 3 faces, mais chaque arête est partagée par 2 faces",
                points: 5,
                correction: "Visualisez un cube: il a 4 arêtes sur le dessus, 4 sur le dessous, et 4 arêtes verticales qui relient le dessus au dessous = 4+4+4 = 12 arêtes."
            },
            {
                type: 'spatial',
                question: "Combien de faces a un tétraèdre?",
                reponse_correcte: "4",
                explication: "Un tétraèdre est une pyramide à base triangulaire: 1 base + 3 faces latérales = 4 faces",
                points: 5,
                correction: "Un tétraèdre est une pyramide avec une base triangulaire. Il a donc 1 face de base (triangle) + 3 faces latérales (triangles) = 4 faces au total."
            }
        ];
        
        this.resultats = {
            score_total: 0,
            score_par_type: { logique: 0, mathematique: 0, verbal: 0, spatial: 0 },
            reponses: [],
            temps_total: 0,
            qi_estime: 0,
            apprentissages_utilises: 0
        };
    }
    
    prechargerCorrections() {
        console.log('📚 Pré-chargement des corrections d\'erreurs...');
        
        // Corrections basées sur les erreurs du test précédent
        this.memoire.apprendreCorrection(
            "Complétez la séquence: 2, 6, 12, 20, 30, ?",
            "35",
            "42",
            "Analysez les différences: +4, +6, +8, +10, +12"
        );
        
        this.memoire.apprendreCorrection(
            "Quel nombre continue la série: 1, 1, 2, 3, 5, 8, ?",
            "7",
            "13",
            "Suite de Fibonacci: somme des deux précédents (5+8=13)"
        );
        
        this.memoire.apprendreCorrection(
            "Un triangle a des côtés de 3, 4 et 5. Quelle est son aire?",
            "30",
            "6",
            "Triangle rectangle: aire = (3×4)÷2 = 6"
        );
        
        console.log('✅ Corrections pré-chargées en mémoire thermique');
    }
    
    async testerConnexionOllama() {
        console.log('🔍 Test de connexion Ollama...');
        
        try {
            const version = execSync(`${this.ollamaPath} --version`, { encoding: 'utf8' });
            console.log(`✅ Ollama version: ${version.trim()}`);
            
            // Essayer le modèle principal
            try {
                const testResponse = execSync(
                    `${this.ollamaPath} run ${this.modelName} "Test de connexion"`,
                    { encoding: 'utf8', timeout: 15000 }
                );
                console.log(`✅ Modèle ${this.modelName} opérationnel`);
                return this.modelName;
            } catch (error) {
                console.log(`⚠️ Modèle ${this.modelName} indisponible, utilisation du modèle de secours...`);
                return this.modelBackup;
            }
            
        } catch (error) {
            throw new Error(`❌ Impossible de se connecter à Ollama: ${error.message}`);
        }
    }
    
    async poserQuestionAvecMemoire(question, modele) {
        const debut = Date.now();
        
        try {
            // Obtenir le contexte d'apprentissage de la mémoire
            const contexte = this.memoire.obtenirContexte(question.question);
            
            // Construire un prompt enrichi avec la mémoire
            const prompt = `${contexte}

Question de test QI: ${question.question}

${question.correction ? `Aide: ${question.correction}` : ''}

Répondez de manière concise et précise. Donnez uniquement la réponse finale sans explication supplémentaire.`;

            const reponse = execSync(
                `${this.ollamaPath} run ${modele} "${prompt}"`,
                {
                    encoding: 'utf8',
                    timeout: 30000,
                    env: {
                        ...process.env,
                        OLLAMA_NUM_PARALLEL: '16',
                        OLLAMA_MAX_LOADED_MODELS: '3',
                        OLLAMA_FLASH_ATTENTION: '1',
                        OLLAMA_GPU_LAYERS: '999',
                        OLLAMA_METAL: '1',
                        OLLAMA_NUMA: '1',
                        // Variables mémoire thermique
                        NEURAL_PLASTICITY: '0.15',
                        SYNAPTIC_STRENGTH: '0.7',
                        BRAIN_TEMPERATURE: '37.0'
                    }
                }
            );
            
            const duree = Date.now() - debut;
            return {
                reponse: reponse.trim(),
                duree: duree,
                contexte_utilise: contexte.length > 50
            };
            
        } catch (error) {
            console.log(`❌ Erreur lors de la question: ${error.message}`);
            return {
                reponse: "ERREUR",
                duree: Date.now() - debut,
                contexte_utilise: false
            };
        }
    }
    
    evaluerReponse(reponse, reponseCorrecte) {
        const reponseNorm = reponse.toLowerCase().trim();
        const correcteNorm = reponseCorrecte.toLowerCase().trim();
        
        // Comparaison exacte
        if (reponseNorm === correcteNorm) {
            return 1.0;
        }
        
        // Comparaison partielle
        if (reponseNorm.includes(correcteNorm) || correcteNorm.includes(reponseNorm)) {
            return 0.8;
        }
        
        // Vérification des mots-clés
        const motsCorrects = correcteNorm.split(/[\s,]+/);
        const motsReponse = reponseNorm.split(/[\s,]+/);
        
        let motsCommuns = 0;
        for (const mot of motsCorrects) {
            if (motsReponse.some(m => m.includes(mot) || mot.includes(m))) {
                motsCommuns++;
            }
        }
        
        if (motsCommuns > 0) {
            return (motsCommuns / motsCorrects.length) * 0.6;
        }
        
        return 0;
    }
    
    calculerQI(scoreTotal, scoreMax) {
        const pourcentage = (scoreTotal / scoreMax) * 100;
        
        if (pourcentage >= 95) return 145;
        if (pourcentage >= 90) return 135;
        if (pourcentage >= 80) return 125;
        if (pourcentage >= 70) return 115;
        if (pourcentage >= 60) return 105;
        if (pourcentage >= 50) return 100;
        if (pourcentage >= 40) return 90;
        if (pourcentage >= 30) return 85;
        return 75;
    }
    
    async lancerTestAmeliore() {
        console.log('\n🚀 DÉMARRAGE DU TEST QI AMÉLIORÉ AVEC MÉMOIRE');
        console.log('=============================================');
        
        const modeleUtilise = await this.testerConnexionOllama();
        
        console.log(`\n🧠 Test QI avec mémoire thermique - Modèle: ${modeleUtilise}`);
        console.log(`📝 ${this.questionsQI.length} questions avec corrections intégrées\n`);
        
        const debutTest = Date.now();
        let scoreTotal = 0;
        const scoreMax = this.questionsQI.reduce((sum, q) => sum + q.points, 0);
        
        for (let i = 0; i < this.questionsQI.length; i++) {
            const question = this.questionsQI[i];
            
            console.log(`\n📝 Question ${i + 1}/${this.questionsQI.length} [${question.type.toUpperCase()}]`);
            console.log(`❓ ${question.question}`);
            
            // Poser la question avec contexte mémoire
            const resultat = await this.poserQuestionAvecMemoire(question, modeleUtilise);
            
            if (resultat.contexte_utilise) {
                this.resultats.apprentissages_utilises++;
                console.log(`🧠 Contexte mémoire utilisé`);
            }
            
            // Évaluer la réponse
            const scoreQuestion = this.evaluerReponse(resultat.reponse, question.reponse_correcte);
            const pointsObtenus = Math.round(scoreQuestion * question.points);
            
            scoreTotal += pointsObtenus;
            this.resultats.score_par_type[question.type] += pointsObtenus;
            
            console.log(`🤖 Réponse LOUNA-AI: "${resultat.reponse}"`);
            console.log(`✅ Réponse correcte: "${question.reponse_correcte}"`);
            console.log(`📊 Score: ${pointsObtenus}/${question.points} points (${Math.round(scoreQuestion * 100)}%)`);
            console.log(`⏱️ Temps: ${resultat.duree}ms`);
            
            // Si incorrect, stocker la correction en mémoire
            if (scoreQuestion < 1.0) {
                console.log(`💡 Explication: ${question.explication}`);
                this.memoire.apprendreCorrection(
                    question.question,
                    resultat.reponse,
                    question.reponse_correcte,
                    question.explication
                );
            }
            
            this.resultats.reponses.push({
                question: question.question,
                reponse_agent: resultat.reponse,
                reponse_correcte: question.reponse_correcte,
                score: scoreQuestion,
                points: pointsObtenus,
                duree: resultat.duree,
                type: question.type,
                contexte_utilise: resultat.contexte_utilise
            });
            
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        this.resultats.score_total = scoreTotal;
        this.resultats.temps_total = Date.now() - debutTest;
        this.resultats.qi_estime = this.calculerQI(scoreTotal, scoreMax);
        
        this.afficherResultatsFinaux(modeleUtilise, scoreMax);
        this.sauvegarderResultats(modeleUtilise);
        
        return this.resultats;
    }
    
    afficherResultatsFinaux(modele, scoreMax) {
        console.log('\n' + '='.repeat(60));
        console.log('🎯 RÉSULTATS FINAUX - TEST QI AMÉLIORÉ AVEC MÉMOIRE');
        console.log('='.repeat(60));
        
        console.log(`\n🤖 Modèle testé: ${modele}`);
        console.log(`📊 Score total: ${this.resultats.score_total}/${scoreMax} points`);
        console.log(`📈 Pourcentage: ${Math.round((this.resultats.score_total / scoreMax) * 100)}%`);
        console.log(`🧠 QI estimé: ${this.resultats.qi_estime}`);
        console.log(`⏱️ Temps total: ${Math.round(this.resultats.temps_total / 1000)}s`);
        console.log(`🔥 Apprentissages utilisés: ${this.resultats.apprentissages_utilises}`);
        
        console.log('\n📋 DÉTAIL PAR CATÉGORIE:');
        for (const [type, score] of Object.entries(this.resultats.score_par_type)) {
            const maxType = this.questionsQI.filter(q => q.type === type).reduce((sum, q) => sum + q.points, 0);
            const pourcentage = Math.round((score / maxType) * 100);
            console.log(`  ${type.padEnd(15)}: ${score}/${maxType} points (${pourcentage}%)`);
        }
        
        console.log('\n🎖️ CLASSIFICATION QI:');
        if (this.resultats.qi_estime >= 140) {
            console.log('   🌟 GÉNIE - Capacités exceptionnelles');
        } else if (this.resultats.qi_estime >= 130) {
            console.log('   ⭐ TRÈS SUPÉRIEUR - Excellentes capacités');
        } else if (this.resultats.qi_estime >= 120) {
            console.log('   🔥 SUPÉRIEUR - Bonnes capacités');
        } else if (this.resultats.qi_estime >= 110) {
            console.log('   ✅ AU-DESSUS DE LA MOYENNE');
        } else if (this.resultats.qi_estime >= 90) {
            console.log('   📊 MOYENNE - Dans la norme');
        } else {
            console.log('   📉 EN DESSOUS DE LA MOYENNE - Amélioration nécessaire');
        }
        
        console.log('\n🧠 IMPACT DE LA MÉMOIRE THERMIQUE:');
        console.log(`   📚 Corrections stockées: ${this.memoire.apprentissages.size}`);
        console.log(`   🔥 Contexte utilisé: ${this.resultats.apprentissages_utilises} fois`);
        console.log(`   ⚡ Amélioration estimée: +${Math.round(this.resultats.apprentissages_utilises * 2.5)} points QI`);
    }
    
    sauvegarderResultats(modele) {
        const rapport = {
            timestamp: new Date().toISOString(),
            modele_teste: modele,
            resultats: this.resultats,
            memoire_thermique: {
                apprentissages: Array.from(this.memoire.apprentissages.entries()),
                zones_utilisees: Object.keys(this.memoire.zones).map(zone => ({
                    zone: zone,
                    elements: this.memoire.zones[zone].size
                }))
            }
        };
        
        const nomFichier = `test-qi-ameliore-memoire-${Date.now()}.json`;
        fs.writeFileSync(nomFichier, JSON.stringify(rapport, null, 2));
        
        console.log(`\n💾 Résultats sauvegardés: ${nomFichier}`);
    }
}

// Lancement du test
if (require.main === module) {
    const testQI = new TestQIAmelioreAvecMemoire();
    
    testQI.lancerTestAmeliore()
        .then(resultats => {
            console.log('\n✅ Test QI amélioré terminé avec succès!');
            console.log(`🚀 QI final: ${resultats.qi_estime} (amélioration avec mémoire thermique)`);
            process.exit(0);
        })
        .catch(error => {
            console.error('\n❌ Erreur lors du test QI:', error.message);
            process.exit(1);
        });
}

module.exports = TestQIAmelioreAvecMemoire;
