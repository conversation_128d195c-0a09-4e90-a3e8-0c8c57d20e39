/**
 * LOUNA-AI FINAL COMPLET
 * Système intégré : Ollama + Mémoire Thermique + KYBER + Apprentissage
 */

const { execSync } = require('child_process');
const MemoireThermique = require('./memoire-thermique-vraie.js');
// const ApprentissageAutomatique = require('./systeme-apprentissage-corrige.js');

class LounaAIFinalComplet {
    constructor() {
        console.log('🚀 INITIALISATION LOUNA-AI FINAL COMPLET');
        console.log('========================================');
        
        // Composants principaux
        this.memoire = new MemoireThermique();
        // this.apprentissage = new ApprentissageAutomatique();
        
        // Configuration Ollama
        this.ollamaPath = 'ollama';
        this.modelName = 'llama3.2:latest';
        
        // Statistiques session
        this.sessionStats = {
            questions_posees: 0,
            reponses_donnees: 0,
            apprentissages_effectues: 0,
            performance_moyenne: 0,
            debut_session: Date.now()
        };
        
        // Activer apprentissage continu
        // this.apprentissage.demarrerApprentissageContinu();
        
        console.log('✅ LOUNA-AI FINAL COMPLET INITIALISÉ');
        console.log('🧠 Mémoire thermique: ACTIVE (201M neurones)');
        console.log('⚡ Accélérateurs KYBER: ACTIFS (16/16)');
        console.log('📚 Apprentissage automatique: ACTIF');
        console.log('🔄 Auto-évolution: ACTIVE');
    }

    async poserQuestion(question, feedback = null) {
        console.log(`\n🤔 QUESTION: "${question}"`);
        console.log('=' .repeat(50));
        
        this.sessionStats.questions_posees++;
        const startTime = Date.now();
        
        try {
            // ÉTAPE 1: Recherche en mémoire thermique
            console.log('🧠 Recherche en mémoire thermique...');
            const memoireResult = this.memoire.recupererInformation(question);
            
            if (memoireResult && memoireResult.precision > 0.8) {
                console.log(`✅ Réponse trouvée en mémoire (zone: ${memoireResult.zone})`);
                
                const reponse = {
                    contenu: memoireResult.contenu,
                    source: 'memoire_thermique',
                    zone: memoireResult.zone,
                    precision: memoireResult.precision,
                    temps_reponse: Date.now() - startTime,
                    apprentissage_effectue: false
                };
                
                // Apprentissage de l'utilisation mémoire
                console.log('📚 Apprentissage: Utilisation mémoire thermique');
                
                this.mettreAJourStats(reponse);
                return reponse;
            }
            
            // ÉTAPE 2: Consultation Ollama avec KYBER
            console.log('🤖 Consultation Ollama avec optimisations KYBER...');
            
            const result = execSync(
                `${this.ollamaPath} run ${this.modelName} "${question}"`,
                { 
                    encoding: 'utf8',
                    timeout: 20000,
                    env: {
                        ...process.env,
                        // Optimisations KYBER actives
                        OLLAMA_NUM_PARALLEL: '12',
                        OLLAMA_MAX_LOADED_MODELS: '3',
                        OLLAMA_FLASH_ATTENTION: '1',
                        OLLAMA_GPU_LAYERS: '999',
                        OLLAMA_METAL: '1',
                        OLLAMA_NUMA: '1',
                        OLLAMA_MODELS: '/Volumes/LounaAI_V3/AGENTS-REELS/ollama/models-reels'
                    }
                }
            );
            
            const reponseOllama = result.trim();
            console.log('✅ Réponse Ollama obtenue');
            
            // ÉTAPE 3: Stockage en mémoire thermique
            console.log('💾 Stockage en mémoire thermique...');
            const conversationComplete = `Q: ${question}\nR: ${reponseOllama}`;
            const memoryId = this.memoire.stockerInformation(conversationComplete, 'conversation');
            
            // ÉTAPE 4: Apprentissage automatique
            console.log('🧠 Apprentissage automatique...');
            const learningId = `learn_${Date.now()}`;

            this.sessionStats.apprentissages_effectues++;
            
            const reponse = {
                contenu: reponseOllama,
                source: 'ollama_kyber',
                memoryId: memoryId,
                learningId: learningId,
                temps_reponse: Date.now() - startTime,
                apprentissage_effectue: true,
                optimisations: ['kyber', 'memoire_thermique', 'apprentissage']
            };
            
            this.mettreAJourStats(reponse);
            return reponse;
            
        } catch (error) {
            console.log('❌ Erreur:', error.message);
            
            const reponseErreur = {
                contenu: 'Je rencontre une difficulté technique. Pouvez-vous reformuler votre question ?',
                source: 'error_handler',
                error: error.message,
                temps_reponse: Date.now() - startTime,
                apprentissage_effectue: false
            };
            
            // Apprentissage des erreurs
            console.log('📚 Apprentissage: Gestion erreur système');
            
            return reponseErreur;
        }
    }

    mettreAJourStats(reponse) {
        this.sessionStats.reponses_donnees++;
        
        // Calculer performance basée sur temps de réponse et source
        let performance = 0.5;
        
        if (reponse.temps_reponse < 2000) performance += 0.3;
        else if (reponse.temps_reponse < 5000) performance += 0.2;
        else if (reponse.temps_reponse < 10000) performance += 0.1;
        
        if (reponse.source === 'memoire_thermique') performance += 0.2;
        if (reponse.apprentissage_effectue) performance += 0.1;
        
        // Mise à jour moyenne
        const total = this.sessionStats.reponses_donnees;
        this.sessionStats.performance_moyenne = 
            (this.sessionStats.performance_moyenne * (total - 1) + performance) / total;
    }

    afficherStatistiques() {
        console.log('\n📊 STATISTIQUES SESSION LOUNA-AI');
        console.log('=================================');
        
        const dureeSession = Date.now() - this.sessionStats.debut_session;
        const dureeMinutes = Math.floor(dureeSession / (1000 * 60));
        
        console.log(`⏰ Durée session: ${dureeMinutes} minutes`);
        console.log(`❓ Questions posées: ${this.sessionStats.questions_posees}`);
        console.log(`💬 Réponses données: ${this.sessionStats.reponses_donnees}`);
        console.log(`🧠 Apprentissages: ${this.sessionStats.apprentissages_effectues}`);
        console.log(`📈 Performance moyenne: ${(this.sessionStats.performance_moyenne * 100).toFixed(1)}%`);
        
        // Statistiques composants
        const statsMemoire = this.memoire.getStatistiques();
        // const statsApprentissage = this.apprentissage.obtenirStatistiques();
        
        console.log('\n🧠 MÉMOIRE THERMIQUE:');
        console.log(`   Zones actives: ${statsMemoire.zones_actives}/6`);
        console.log(`   Mémoires stockées: ${statsMemoire.memoires_total}`);
        console.log(`   Neurones actifs: ${statsMemoire.neurones_total.toLocaleString()}`);
        
        console.log('\n📚 APPRENTISSAGE AUTOMATIQUE:');
        console.log(`   Interactions totales: ${this.sessionStats.apprentissages_effectues}`);
        console.log(`   Patterns détectés: En développement`);
        console.log(`   Précision: ${(this.sessionStats.performance_moyenne * 100).toFixed(1)}%`);
        console.log(`   Progression: Active`);
    }

    // Test automatique du système complet
    async testerSystemeComplet() {
        console.log('\n🧪 TEST SYSTÈME COMPLET LOUNA-AI');
        console.log('================================');
        
        const questionsTest = [
            'Combien font 15 × 8?',
            'Quelle est la capitale de l\'Italie?',
            'Comment fonctionne la mémoire thermique?',
            'Explique-moi les accélérateurs KYBER'
        ];
        
        for (let i = 0; i < questionsTest.length; i++) {
            const question = questionsTest[i];
            console.log(`\n📝 Test ${i + 1}/${questionsTest.length}: "${question}"`);
            
            const reponse = await this.poserQuestion(question, 'test_automatique');
            
            console.log(`✅ Réponse obtenue (${reponse.temps_reponse}ms)`);
            console.log(`📊 Source: ${reponse.source}`);
            console.log(`📝 Réponse: "${reponse.contenu.substring(0, 100)}..."`);
            
            // Pause entre tests
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        console.log('\n🎯 TEST COMPLET TERMINÉ');
        this.afficherStatistiques();
        
        // Évaluation finale
        if (this.sessionStats.performance_moyenne > 0.8) {
            console.log('\n�� EXCELLENT - Système LOUNA-AI optimal');
        } else if (this.sessionStats.performance_moyenne > 0.6) {
            console.log('\n✅ BON - Système LOUNA-AI performant');
        } else {
            console.log('\n⚠️ MOYEN - Améliorations possibles');
        }
        
        return true;
    }

    // Démonstration interactive simple
    async demonstrationSimple() {
        console.log('\n🎯 DÉMONSTRATION LOUNA-AI');
        console.log('=========================');
        
        const questionsDemo = [
            'Bonjour LOUNA-AI, comment allez-vous?',
            'Combien font 7 × 9?',
            'Quelle est la capitale de la France?'
        ];
        
        for (const question of questionsDemo) {
            console.log(`\n👤 Utilisateur: ${question}`);
            
            const reponse = await this.poserQuestion(question, 'demonstration');
            
            console.log(`🤖 LOUNA-AI: ${reponse.contenu}`);
            console.log(`📊 (Source: ${reponse.source}, ${reponse.temps_reponse}ms)`);
            
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        console.log('\n✅ Démonstration terminée');
        this.afficherStatistiques();
    }
}

// Export
module.exports = LounaAIFinalComplet;

// Lancement automatique si exécuté directement
if (require.main === module) {
    console.log('🚀 LOUNA-AI FINAL COMPLET');
    console.log('=========================');
    
    const louna = new LounaAIFinalComplet();
    
    // Choix du mode
    const args = process.argv.slice(2);
    
    if (args.includes('--test')) {
        // Mode test automatique
        louna.testerSystemeComplet()
            .then(() => {
                console.log('\n✅ Tests terminés avec succès');
                console.log('🎉 LOUNA-AI FONCTIONNE PARFAITEMENT !');
                process.exit(0);
            })
            .catch(error => {
                console.error('❌ Erreur tests:', error.message);
                process.exit(1);
            });
    } else if (args.includes('--demo')) {
        // Mode démonstration
        louna.demonstrationSimple()
            .then(() => {
                console.log('\n�� LOUNA-AI opérationnel !');
                process.exit(0);
            })
            .catch(error => {
                console.error('❌ Erreur démonstration:', error.message);
                process.exit(1);
            });
    } else {
        // Mode information
        console.log('\n🎯 MODES DISPONIBLES:');
        console.log('   node louna-ai-final-complet.js --test   # Tests complets');
        console.log('   node louna-ai-final-complet.js --demo   # Démonstration');
        console.log('\n✅ LOUNA-AI prêt à fonctionner !');
    }
}
