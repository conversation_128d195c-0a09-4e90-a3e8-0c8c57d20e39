#!/usr/bin/env node

/**
 * MÉMOIRE THERMIQUE RÉELLE CONNECTÉE
 * Utilise les vraies données existantes dans MEMOIRE-REELLE
 */

const fs = require('fs');
const path = require('path');

class MemoireThermique {
    constructor() {
        console.log('🔥 ========================================');
        console.log('🧠 MÉMOIRE THERMIQUE RÉELLE CONNECTÉE');
        console.log('🔥 ========================================');
        
        this.cheminBase = './MEMOIRE-REELLE';
        this.zones = {
            zone1: { nom: 'INSTANT', temperature: 70, neurones: 0, actifs: 0 },
            zone2: { nom: 'SHORT_TERM', temperature: 60, neurones: 0, actifs: 0 },
            zone3: { nom: 'WORKING', temperature: 50, neurones: 0, actifs: 0 },
            zone4: { nom: 'LONG_TERM', temperature: 40, neurones: 0, actifs: 0 },
            zone5: { nom: 'EMOTIONAL', temperature: 30, neurones: 0, actifs: 0 },
            zone6: { nom: 'CREATIVE', temperature: 20, neurones: 0, actifs: 0 }
        };
        
        this.statistiques = {
            neurones_totaux: 0,
            neurones_actifs: 0,
            synapses_totales: 0,
            synapses_actives: 0,
            flux_neural: 0,
            temperature_moyenne: 0
        };
        
        this.initialiser();
    }

    // INITIALISER MÉMOIRE THERMIQUE
    initialiser() {
        try {
            this.verifierStructure();
            this.chargerDonneesReelles();
            this.calculerStatistiques();
            this.demarrerMonitoring();
            
            console.log('✅ Mémoire thermique réelle initialisée');
            console.log(`🧠 ${this.statistiques.neurones_totaux.toLocaleString()} neurones détectés`);
            console.log(`⚡ ${this.statistiques.neurones_actifs.toLocaleString()} neurones actifs`);
            console.log(`🌡️ Température moyenne: ${this.statistiques.temperature_moyenne.toFixed(1)}°C`);
            
        } catch (error) {
            console.log('❌ Erreur initialisation mémoire thermique:', error.message);
        }
    }

    // VÉRIFIER STRUCTURE
    verifierStructure() {
        if (!fs.existsSync(this.cheminBase)) {
            throw new Error('MEMOIRE-REELLE non trouvée');
        }
        
        const zonesPath = path.join(this.cheminBase, 'zones-thermiques');
        if (!fs.existsSync(zonesPath)) {
            throw new Error('Zones thermiques non trouvées');
        }
        
        console.log('✅ Structure mémoire thermique vérifiée');
    }

    // CHARGER DONNÉES RÉELLES
    chargerDonneesReelles() {
        Object.keys(this.zones).forEach(zoneId => {
            const zone = this.zones[zoneId];
            const cheminZone = path.join(this.cheminBase, 'zones-thermiques', `${zoneId}_${zone.temperature}C`);
            
            if (fs.existsSync(cheminZone)) {
                // Compter les neurones réels
                const fichiers = fs.readdirSync(cheminZone);
                const neurones = fichiers.filter(f => f.startsWith('neurone_') && f.endsWith('.json'));
                
                zone.neurones = neurones.length;
                
                // Compter neurones actifs
                let actifs = 0;
                neurones.forEach(neuroneFile => {
                    try {
                        const neuroneData = JSON.parse(fs.readFileSync(path.join(cheminZone, neuroneFile), 'utf8'));
                        if (neuroneData.etat === 'actif') {
                            actifs++;
                        }
                    } catch (error) {
                        // Ignorer fichiers corrompus
                    }
                });
                
                zone.actifs = actifs;
                
                console.log(`🔗 Zone ${zone.nom}: ${zone.neurones} neurones (${zone.actifs} actifs)`);
            }
        });
    }

    // CALCULER STATISTIQUES
    calculerStatistiques() {
        this.statistiques.neurones_totaux = Object.values(this.zones)
            .reduce((sum, zone) => sum + zone.neurones, 0);
        
        this.statistiques.neurones_actifs = Object.values(this.zones)
            .reduce((sum, zone) => sum + zone.actifs, 0);
        
        // Estimer synapses (environ 7000 par neurone)
        this.statistiques.synapses_totales = this.statistiques.neurones_totaux * 7000;
        this.statistiques.synapses_actives = this.statistiques.neurones_actifs * 7000;
        
        // Calculer flux neural
        this.statistiques.flux_neural = this.statistiques.neurones_actifs * 50; // 50 ops/sec par neurone
        
        // Température moyenne
        this.statistiques.temperature_moyenne = Object.values(this.zones)
            .reduce((sum, zone) => sum + zone.temperature, 0) / Object.keys(this.zones).length;
    }

    // DÉMARRER MONITORING
    demarrerMonitoring() {
        // Mise à jour toutes les 30 secondes
        setInterval(() => {
            this.mettreAJourDonnees();
        }, 30000);
    }

    // METTRE À JOUR DONNÉES
    mettreAJourDonnees() {
        try {
            this.chargerDonneesReelles();
            this.calculerStatistiques();
            this.sauvegarderEtat();
        } catch (error) {
            console.log('⚠️ Erreur mise à jour mémoire thermique:', error.message);
        }
    }

    // SAUVEGARDER ÉTAT
    sauvegarderEtat() {
        try {
            const etat = {
                timestamp: new Date().toISOString(),
                zones: this.zones,
                statistiques: this.statistiques
            };
            
            const fichierEtat = path.join(this.cheminBase, 'etat-memoire-thermique.json');
            fs.writeFileSync(fichierEtat, JSON.stringify(etat, null, 2));
            
        } catch (error) {
            console.log('❌ Erreur sauvegarde état:', error.message);
        }
    }

    // AJOUTER SOUVENIR
    ajouterSouvenir(contenu, type = 'general', zone = 'zone1') {
        try {
            const souvenir = {
                id: `souvenir_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                contenu: contenu,
                type: type,
                zone: zone,
                timestamp: new Date().toISOString(),
                activations: 0,
                force: 1.0
            };
            
            // Sauvegarder dans la zone appropriée
            const cheminZone = path.join(this.cheminBase, 'zones-thermiques', `${zone}_${this.zones[zone].temperature}C`);
            const fichierSouvenir = path.join(cheminZone, `${souvenir.id}.json`);
            
            fs.writeFileSync(fichierSouvenir, JSON.stringify(souvenir, null, 2));
            
            // Sauvegarder aussi dans zone1 pour sauvegarde continue
            const sauvegardeZone1 = path.join(this.cheminBase, 'zone1-sauvegarde-continue');
            const fichierSauvegarde = path.join(sauvegardeZone1, `${Date.now()}_${souvenir.id}.json`);
            fs.writeFileSync(fichierSauvegarde, JSON.stringify(souvenir, null, 2));
            
            console.log(`💾 Souvenir ajouté: ${souvenir.id} (${zone})`);
            return souvenir.id;
            
        } catch (error) {
            console.log('❌ Erreur ajout souvenir:', error.message);
            return null;
        }
    }

    // RECHERCHER SOUVENIRS
    rechercherSouvenirs(terme, limite = 10) {
        const resultats = [];
        
        try {
            Object.keys(this.zones).forEach(zoneId => {
                const zone = this.zones[zoneId];
                const cheminZone = path.join(this.cheminBase, 'zones-thermiques', `${zoneId}_${zone.temperature}C`);
                
                if (fs.existsSync(cheminZone)) {
                    const fichiers = fs.readdirSync(cheminZone);
                    
                    fichiers.forEach(fichier => {
                        if (fichier.startsWith('souvenir_') && fichier.endsWith('.json')) {
                            try {
                                const souvenir = JSON.parse(fs.readFileSync(path.join(cheminZone, fichier), 'utf8'));
                                
                                if (souvenir.contenu && souvenir.contenu.toLowerCase().includes(terme.toLowerCase())) {
                                    resultats.push({
                                        ...souvenir,
                                        zone_nom: zone.nom,
                                        zone_temperature: zone.temperature
                                    });
                                }
                            } catch (error) {
                                // Ignorer fichiers corrompus
                            }
                        }
                    });
                }
            });
            
            // Trier par pertinence et limiter
            return resultats
                .sort((a, b) => b.force - a.force)
                .slice(0, limite);
                
        } catch (error) {
            console.log('❌ Erreur recherche souvenirs:', error.message);
            return [];
        }
    }

    // OBTENIR ÉTAT COMPLET
    obtenirEtatComplet() {
        return {
            timestamp: new Date().toISOString(),
            zones: Object.entries(this.zones).map(([id, zone]) => ({
                id: id,
                nom: zone.nom,
                temperature: zone.temperature,
                neurones_totaux: zone.neurones,
                neurones_actifs: zone.actifs,
                pourcentage_activite: zone.neurones > 0 ? ((zone.actifs / zone.neurones) * 100).toFixed(1) : 0,
                synapses_estimees: zone.neurones * 7000,
                synapses_actives: zone.actifs * 7000,
                flux_neural: zone.actifs * 50
            })),
            statistiques_globales: {
                neurones_totaux: this.statistiques.neurones_totaux.toLocaleString(),
                neurones_actifs: this.statistiques.neurones_actifs.toLocaleString(),
                pourcentage_activite_globale: this.statistiques.neurones_totaux > 0 ? 
                    ((this.statistiques.neurones_actifs / this.statistiques.neurones_totaux) * 100).toFixed(2) : 0,
                synapses_totales: this.statistiques.synapses_totales.toLocaleString(),
                synapses_actives: this.statistiques.synapses_actives.toLocaleString(),
                flux_neural_global: this.statistiques.flux_neural.toLocaleString() + ' ops/sec',
                temperature_moyenne: this.statistiques.temperature_moyenne.toFixed(1) + '°C'
            },
            connexion_status: {
                structure_ok: fs.existsSync(this.cheminBase),
                zones_detectees: Object.keys(this.zones).length,
                zones_actives: Object.values(this.zones).filter(z => z.neurones > 0).length,
                derniere_mise_a_jour: new Date().toISOString()
            }
        };
    }

    // CRÉER NEURONE
    creerNeurone(zone = 'zone1', type = 'general') {
        try {
            const neurone = {
                id: `neurone_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                zone: zone,
                type: type,
                etat: 'actif',
                synapses: [],
                activations: 0,
                force_synaptique: 0.5,
                plasticite: 0.1,
                date_creation: Date.now(),
                derniere_activation: null,
                specialisation: 'neurone_general',
                seuil_activation: 0.5,
                periode_refractaire: 5,
                zone_thermique: zone,
                temperature_actuelle: this.zones[zone].temperature,
                historique_thermique: [{
                    zone: zone,
                    temperature: this.zones[zone].temperature,
                    timestamp: Date.now(),
                    raison: 'creation'
                }]
            };
            
            const cheminZone = path.join(this.cheminBase, 'zones-thermiques', `${zone}_${this.zones[zone].temperature}C`);
            const fichierNeurone = path.join(cheminZone, `${neurone.id}.json`);
            
            fs.writeFileSync(fichierNeurone, JSON.stringify(neurone, null, 2));
            
            console.log(`🧠 Neurone créé: ${neurone.id} (${zone})`);
            return neurone.id;
            
        } catch (error) {
            console.log('❌ Erreur création neurone:', error.message);
            return null;
        }
    }

    // MIGRER NEURONE
    migrerNeurone(neuroneId, zoneSource, zoneDestination) {
        try {
            const cheminSource = path.join(this.cheminBase, 'zones-thermiques', `${zoneSource}_${this.zones[zoneSource].temperature}C`);
            const cheminDestination = path.join(this.cheminBase, 'zones-thermiques', `${zoneDestination}_${this.zones[zoneDestination].temperature}C`);
            
            const fichierSource = path.join(cheminSource, `${neuroneId}.json`);
            const fichierDestination = path.join(cheminDestination, `${neuroneId}.json`);
            
            if (fs.existsSync(fichierSource)) {
                const neurone = JSON.parse(fs.readFileSync(fichierSource, 'utf8'));
                
                // Mettre à jour les données du neurone
                neurone.zone_thermique = zoneDestination;
                neurone.temperature_actuelle = this.zones[zoneDestination].temperature;
                neurone.derniere_migration = Date.now();
                
                // Ajouter à l'historique
                neurone.historique_thermique.push({
                    zone: zoneDestination,
                    temperature: this.zones[zoneDestination].temperature,
                    timestamp: Date.now(),
                    raison: 'migration_manuelle'
                });
                
                // Sauvegarder dans la nouvelle zone
                fs.writeFileSync(fichierDestination, JSON.stringify(neurone, null, 2));
                
                // Supprimer de l'ancienne zone
                fs.unlinkSync(fichierSource);
                
                console.log(`🔄 Neurone migré: ${neuroneId} (${zoneSource} → ${zoneDestination})`);
                return true;
            }
            
            return false;
            
        } catch (error) {
            console.log('❌ Erreur migration neurone:', error.message);
            return false;
        }
    }
}

module.exports = MemoireThermique;
