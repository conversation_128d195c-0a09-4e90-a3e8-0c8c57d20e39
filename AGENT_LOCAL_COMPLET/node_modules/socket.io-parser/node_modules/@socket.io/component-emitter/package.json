{"name": "@socket.io/component-emitter", "description": "Event emitter", "version": "3.1.2", "license": "MIT", "devDependencies": {"mocha": "*", "should": "*"}, "component": {"scripts": {"emitter/index.js": "index.js"}}, "main": "./lib/cjs/index.js", "module": "./lib/esm/index.js", "types": "./lib/cjs/index.d.ts", "repository": {"type": "git", "url": "https://github.com/socketio/emitter.git"}, "scripts": {"test": "make test"}, "files": ["lib/"]}