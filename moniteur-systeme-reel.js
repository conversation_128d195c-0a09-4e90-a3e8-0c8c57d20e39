#!/usr/bin/env node

/**
 * MONITEUR SYSTÈME RÉEL - LOUNA-AI
 * Surveillance complète du système en temps réel
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class MoniteurSystemeReel {
    constructor() {
        this.metriques = {
            systeme: {
                cpu_usage: 0,
                memory_usage: 0,
                disk_usage: 0,
                temperature: 0,
                uptime: 0
            },
            ollama: {
                status: 'unknown',
                models_loaded: 0,
                response_time: 0,
                gpu_usage: 0
            },
            louna: {
                qi_actuel: 120,
                sessions_actives: 0,
                cache_hits: 0,
                erreurs: 0
            },
            performance: {
                requests_per_minute: 0,
                average_response_time: 0,
                success_rate: 100
            }
        };
        
        this.alertes = [];
        this.historique = [];
        this.seuils = {
            cpu_critique: 90,
            memory_critique: 95,
            temperature_critique: 80,
            response_time_critique: 5000
        };
        
        this.demarrerMonitoring();
    }

    // SURVEILLANCE SYSTÈME
    surveillerSysteme() {
        try {
            // CPU
            const cpuInfo = execSync('top -l 1 -n 0 | grep "CPU usage"', { encoding: 'utf8' });
            this.metriques.systeme.cpu_usage = this.extraireCPU(cpuInfo);
            
            // Mémoire
            const memInfo = execSync('vm_stat', { encoding: 'utf8' });
            this.metriques.systeme.memory_usage = this.extraireMemoire(memInfo);
            
            // Disque
            const diskInfo = execSync('df -h /', { encoding: 'utf8' });
            this.metriques.systeme.disk_usage = this.extraireDisque(diskInfo);
            
            // Température (si disponible)
            try {
                const tempInfo = execSync('sudo powermetrics -n 1 -i 1000 --samplers smc | grep "CPU die temperature"', { encoding: 'utf8' });
                this.metriques.systeme.temperature = this.extraireTemperature(tempInfo);
            } catch (error) {
                this.metriques.systeme.temperature = 45; // Valeur par défaut
            }
            
            // Uptime
            this.metriques.systeme.uptime = process.uptime();
            
        } catch (error) {
            console.log('⚠️ Erreur surveillance système:', error.message);
        }
    }

    // SURVEILLANCE OLLAMA
    surveillerOllama() {
        try {
            // Status Ollama
            const startTime = Date.now();
            try {
                const response = execSync('curl -s http://localhost:11434/api/tags', { timeout: 3000 });
                this.metriques.ollama.status = 'running';
                this.metriques.ollama.response_time = Date.now() - startTime;
                
                const models = JSON.parse(response);
                this.metriques.ollama.models_loaded = models.models ? models.models.length : 0;
            } catch (error) {
                this.metriques.ollama.status = 'stopped';
                this.metriques.ollama.response_time = 0;
                this.metriques.ollama.models_loaded = 0;
            }
            
            // GPU Usage (si disponible)
            try {
                const gpuInfo = execSync('nvidia-smi --query-gpu=utilization.gpu --format=csv,noheader,nounits', { encoding: 'utf8' });
                this.metriques.ollama.gpu_usage = parseInt(gpuInfo.trim());
            } catch (error) {
                this.metriques.ollama.gpu_usage = 0; // Pas de GPU NVIDIA
            }
            
        } catch (error) {
            console.log('⚠️ Erreur surveillance Ollama:', error.message);
        }
    }

    // SURVEILLANCE LOUNA-AI
    surveillerLouna() {
        try {
            // QI actuel (depuis évolution)
            const evolutionFile = path.join(__dirname, 'MEMOIRE-REELLE', 'evolution-qi.json');
            if (fs.existsSync(evolutionFile)) {
                const data = JSON.parse(fs.readFileSync(evolutionFile, 'utf8'));
                this.metriques.louna.qi_actuel = data.qiActuel || 120;
            }
            
            // Cache hits (depuis accélérateur)
            const cacheFile = path.join(__dirname, 'MEMOIRE-REELLE', 'cache-reponses.json');
            if (fs.existsSync(cacheFile)) {
                const data = JSON.parse(fs.readFileSync(cacheFile, 'utf8'));
                this.metriques.louna.cache_hits = data.stats ? data.stats.hits : 0;
            }
            
            // Sessions actives (estimation)
            this.metriques.louna.sessions_actives = this.estimerSessionsActives();
            
        } catch (error) {
            console.log('⚠️ Erreur surveillance LOUNA:', error.message);
        }
    }

    // DÉTECTION ALERTES
    detecterAlertes() {
        const nouvelles_alertes = [];
        
        // CPU critique
        if (this.metriques.systeme.cpu_usage > this.seuils.cpu_critique) {
            nouvelles_alertes.push({
                type: 'CRITIQUE',
                message: `CPU usage critique: ${this.metriques.systeme.cpu_usage}%`,
                timestamp: Date.now()
            });
        }
        
        // Mémoire critique
        if (this.metriques.systeme.memory_usage > this.seuils.memory_critique) {
            nouvelles_alertes.push({
                type: 'CRITIQUE',
                message: `Mémoire critique: ${this.metriques.systeme.memory_usage}%`,
                timestamp: Date.now()
            });
        }
        
        // Ollama arrêté
        if (this.metriques.ollama.status === 'stopped') {
            nouvelles_alertes.push({
                type: 'ERREUR',
                message: 'Ollama n\'est pas démarré',
                timestamp: Date.now()
            });
        }
        
        // Temps de réponse lent
        if (this.metriques.ollama.response_time > this.seuils.response_time_critique) {
            nouvelles_alertes.push({
                type: 'ATTENTION',
                message: `Temps de réponse lent: ${this.metriques.ollama.response_time}ms`,
                timestamp: Date.now()
            });
        }
        
        // Ajouter nouvelles alertes
        for (const alerte of nouvelles_alertes) {
            this.alertes.push(alerte);
            this.afficherAlerte(alerte);
        }
        
        // Garder seulement les 50 dernières alertes
        if (this.alertes.length > 50) {
            this.alertes = this.alertes.slice(-50);
        }
    }

    afficherAlerte(alerte) {
        const emoji = alerte.type === 'CRITIQUE' ? '🚨' : alerte.type === 'ERREUR' ? '❌' : '⚠️';
        console.log(`${emoji} ${alerte.type}: ${alerte.message}`);
    }

    // UTILITAIRES EXTRACTION
    extraireCPU(cpuInfo) {
        const match = cpuInfo.match(/(\d+\.\d+)%\s+user/);
        return match ? parseFloat(match[1]) : 0;
    }

    extraireMemoire(memInfo) {
        const lines = memInfo.split('\n');
        let total = 0, used = 0;
        
        for (const line of lines) {
            if (line.includes('Pages free:')) {
                const free = parseInt(line.match(/\d+/)[0]) * 4096;
                total += free;
            }
            if (line.includes('Pages active:')) {
                const active = parseInt(line.match(/\d+/)[0]) * 4096;
                used += active;
                total += active;
            }
        }
        
        return total > 0 ? Math.round((used / total) * 100) : 0;
    }

    extraireDisque(diskInfo) {
        const lines = diskInfo.split('\n');
        for (const line of lines) {
            if (line.includes('/')) {
                const parts = line.split(/\s+/);
                const usage = parts[4];
                return parseInt(usage.replace('%', ''));
            }
        }
        return 0;
    }

    extraireTemperature(tempInfo) {
        const match = tempInfo.match(/(\d+\.\d+)/);
        return match ? parseFloat(match[1]) : 45;
    }

    estimerSessionsActives() {
        // Estimation basée sur les processus Node.js actifs
        try {
            const processes = execSync('ps aux | grep node | grep -v grep | wc -l', { encoding: 'utf8' });
            return parseInt(processes.trim()) || 0;
        } catch (error) {
            return 0;
        }
    }

    // GÉNÉRATION RAPPORT
    genererRapport() {
        const rapport = {
            timestamp: new Date().toISOString(),
            status_global: this.determinerStatusGlobal(),
            metriques: { ...this.metriques },
            alertes_actives: this.alertes.filter(a => Date.now() - a.timestamp < 300000), // 5 min
            recommandations: this.genererRecommandations()
        };
        
        return rapport;
    }

    determinerStatusGlobal() {
        if (this.alertes.some(a => a.type === 'CRITIQUE' && Date.now() - a.timestamp < 300000)) {
            return '🔴 CRITIQUE';
        }
        if (this.alertes.some(a => a.type === 'ERREUR' && Date.now() - a.timestamp < 300000)) {
            return '🟡 ATTENTION';
        }
        return '🟢 OPTIMAL';
    }

    genererRecommandations() {
        const recommandations = [];
        
        if (this.metriques.systeme.cpu_usage > 70) {
            recommandations.push('Réduire la charge CPU ou augmenter les ressources');
        }
        
        if (this.metriques.systeme.memory_usage > 80) {
            recommandations.push('Libérer de la mémoire ou optimiser les processus');
        }
        
        if (this.metriques.ollama.response_time > 3000) {
            recommandations.push('Optimiser la configuration Ollama ou vérifier le GPU');
        }
        
        return recommandations;
    }

    // SAUVEGARDE HISTORIQUE
    sauvegarderHistorique() {
        const rapport = this.genererRapport();
        this.historique.push(rapport);
        
        // Garder seulement les 1000 dernières mesures
        if (this.historique.length > 1000) {
            this.historique = this.historique.slice(-1000);
        }
        
        try {
            const fichier = path.join(__dirname, 'MEMOIRE-REELLE', 'monitoring-historique.json');
            const dir = path.dirname(fichier);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
            
            fs.writeFileSync(fichier, JSON.stringify(this.historique, null, 2));
        } catch (error) {
            console.log('⚠️ Erreur sauvegarde historique:', error.message);
        }
    }

    // DÉMARRAGE MONITORING
    demarrerMonitoring() {
        console.log('📊 ========================================');
        console.log('🔍 MONITEUR SYSTÈME RÉEL DÉMARRÉ');
        console.log('📊 ========================================');
        
        // Surveillance continue toutes les 10 secondes
        setInterval(() => {
            this.surveillerSysteme();
            this.surveillerOllama();
            this.surveillerLouna();
            this.detecterAlertes();
            this.afficherStatus();
        }, 10000);
        
        // Sauvegarde historique toutes les minutes
        setInterval(() => {
            this.sauvegarderHistorique();
        }, 60000);
        
        // Premier scan
        this.surveillerSysteme();
        this.surveillerOllama();
        this.surveillerLouna();
    }

    afficherStatus() {
        const status = this.determinerStatusGlobal();
        console.log(`${status} | CPU: ${this.metriques.systeme.cpu_usage}% | RAM: ${this.metriques.systeme.memory_usage}% | QI: ${this.metriques.louna.qi_actuel} | Ollama: ${this.metriques.ollama.status}`);
    }

    // API
    obtenirMetriques() {
        return this.genererRapport();
    }
}

// Démarrer le moniteur
const moniteur = new MoniteurSystemeReel();

// Export
module.exports = moniteur;

// Si lancé directement
if (require.main === module) {
    console.log('🚀 Moniteur système actif...');
    console.log('🛑 Appuyez sur Ctrl+C pour arrêter');
    
    process.on('SIGINT', () => {
        moniteur.sauvegarderHistorique();
        console.log('\n🛑 Arrêt moniteur système');
        process.exit(0);
    });
}
