{"parent": null, "pid": 67747, "argv": ["/Users/<USER>/.nvm/versions/node/v22.4.1/bin/node", "/Users/<USER>/winston-transport/node_modules/.bin/mocha", "test/index.test.js", "test/inheritance.test.js", "test/legacy.test.js"], "execArgv": [], "cwd": "/Users/<USER>/winston-transport", "time": 1731206309841, "ppid": 67746, "coverageFilename": "/Users/<USER>/winston-transport/.nyc_output/68e78020-c804-4f37-a68a-a967c6380da9.json", "externalId": "", "uuid": "68e78020-c804-4f37-a68a-a967c6380da9", "files": ["/Users/<USER>/winston-transport/index.js", "/Users/<USER>/winston-transport/modern.js", "/Users/<USER>/winston-transport/legacy.js"]}