<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Tray-Win32-Dark.ico</key>
		<data>
		Oo1pC94Qno5Ti18quriaceUplCw=
		</data>
		<key>Resources/Tray-Win32.ico</key>
		<data>
		HiyGPWHpcefRThfnouwfLX7k0iE=
		</data>
		<key>Resources/TrayIconTemplate-Dark.png</key>
		<data>
		UoiHrK1s4pQfzWXH2HQalWra9tE=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		l8Wi1vuKPRUMwNWzLcxIGAqLiP0=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		t8bZrBpxglXNJUARmXsqwbj2ciU=
		</data>
		<key>Resources/TrayIconTemplate.png</key>
		<data>
		P6IgA95iHqan4Z10GWqXaKZeO0g=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		r4uRdFQ2CyYkAEN6p4BQKOFERnU=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		EOGEci3Po1ZzlhbZmCu9xV6T8zU=
		</data>
		<key>Resources/app.asar</key>
		<data>
		16Xivnl/A8vCrCZ+YkjbGEl03L0=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/claude-native/artifacts/claude-native.darwin-arm64.node</key>
		<data>
		46Dwaao1sv29T0fjY2exMPrwlnc=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/claude-native/artifacts/claude-native.darwin-x64.node</key>
		<data>
		2+B4Sqq/iAt/u9unz00FviyY4FY=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/claude-native/claude-native-binding.node</key>
		<data>
		oGdJ2C4rEl52sWYmyr7ZC7MLC2o=
		</data>
		<key>Resources/build-props.json</key>
		<data>
		EXXv9PHuVvzYqMIwB+8Gf/yRYxQ=
		</data>
		<key>Resources/de-DE.json</key>
		<data>
		mg7FUlCXE4FUdEFTagXasczGPGM=
		</data>
		<key>Resources/electron.icns</key>
		<data>
		eCyMIPKhv1S2uMawWtntGmR76Sw=
		</data>
		<key>Resources/en-US.json</key>
		<data>
		8QUxQ+gnDk9fRrIZNPKfVsCn8Gw=
		</data>
		<key>Resources/en-XA.json</key>
		<data>
		ceyQMe2W5bCYgHe6CNynCBiyiiQ=
		</data>
		<key>Resources/en-XB.json</key>
		<data>
		dKVuiR/XEnfl9W0ZlF865eBVtqk=
		</data>
		<key>Resources/es-419.json</key>
		<data>
		fK9mJauSRFiR4nBpIOh+TT9W0YY=
		</data>
		<key>Resources/es-ES.json</key>
		<data>
		DxudrZSW4YJ3rYdXCj8Vj0jnqes=
		</data>
		<key>Resources/fr-FR.json</key>
		<data>
		qEmd6ftnwK4kowK1iXWx1x0gd5U=
		</data>
		<key>Resources/hi-IN.json</key>
		<data>
		TGLkqrgpz/k7P6AyNTb5rJ/8JBk=
		</data>
		<key>Resources/id-ID.json</key>
		<data>
		zh+zGudcRzl+qcqbsdmgqtCDxwU=
		</data>
		<key>Resources/it-IT.json</key>
		<data>
		dMIX5K7xi4jJl1Hjn99rwVAA8QU=
		</data>
		<key>Resources/ja-JP.json</key>
		<data>
		LWrkSxsE8a11anVBuqJDjM5qfT4=
		</data>
		<key>Resources/ko-KR.json</key>
		<data>
		plUTBkb2Bp80+jz44aCM0RbKh8w=
		</data>
		<key>Resources/pt-BR.json</key>
		<data>
		43DFkj/C7fe5l50yk9LK8j75xo4=
		</data>
		<key>Resources/xx-AC.json</key>
		<data>
		/NarQ+z0JEr0rr/U+IutJDtB6N4=
		</data>
		<key>Resources/xx-HA.json</key>
		<data>
		edbTBQNQKyXuH9jJEHADUWSv7fM=
		</data>
		<key>Resources/xx-LS.json</key>
		<data>
		VRzUKGJ+HPqDR8wDd2HkBGQAxyE=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/Claude Helper (GPU).app</key>
		<dict>
			<key>cdhash</key>
			<data>
			+ime1aGpWCymayIXsChMCON7rko=
			</data>
			<key>requirement</key>
			<string>identifier "com.anthropic.claudefordesktop.helper" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = Q6L2SF6YDW</string>
		</dict>
		<key>Frameworks/Claude Helper (Plugin).app</key>
		<dict>
			<key>cdhash</key>
			<data>
			k3Maa/wm7izlYnmYAVjmZFuxuzM=
			</data>
			<key>requirement</key>
			<string>identifier "com.anthropic.claudefordesktop.helper" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = Q6L2SF6YDW</string>
		</dict>
		<key>Frameworks/Claude Helper (Renderer).app</key>
		<dict>
			<key>cdhash</key>
			<data>
			SxbRc8g6HzXyx1X3fmqydN4KAbA=
			</data>
			<key>requirement</key>
			<string>identifier "com.anthropic.claudefordesktop.helper" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = Q6L2SF6YDW</string>
		</dict>
		<key>Frameworks/Claude Helper.app</key>
		<dict>
			<key>cdhash</key>
			<data>
			OMcMFW4D+26qPF1tODsJPjVo7xU=
			</data>
			<key>requirement</key>
			<string>identifier "com.anthropic.claudefordesktop.helper" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = Q6L2SF6YDW</string>
		</dict>
		<key>Frameworks/Electron Framework.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			0/OKeUWcIdZHAZgJFgBc0naxWuE=
			</data>
			<key>requirement</key>
			<string>identifier "com.github.Electron.framework" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = Q6L2SF6YDW</string>
		</dict>
		<key>Frameworks/Mantle.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			lXz69Gvr0h/VGB+y6YZFZHStLC8=
			</data>
			<key>requirement</key>
			<string>identifier "org.mantle.Mantle" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = Q6L2SF6YDW</string>
		</dict>
		<key>Frameworks/ReactiveObjC.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			tt975UuJBorLOLXnPvXFqCrCI1M=
			</data>
			<key>requirement</key>
			<string>identifier "com.electron.reactive" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = Q6L2SF6YDW</string>
		</dict>
		<key>Frameworks/Squirrel.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			fTBrPXkfIfX0wQQlsVSqCf3NwX8=
			</data>
			<key>requirement</key>
			<string>identifier "com.github.Squirrel" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = Q6L2SF6YDW</string>
		</dict>
		<key>Resources/Tray-Win32-Dark.ico</key>
		<dict>
			<key>hash2</key>
			<data>
			9nmK1XjokoupOq1xgppj6sLpvC3kB5iGh43bi03yrE8=
			</data>
		</dict>
		<key>Resources/Tray-Win32.ico</key>
		<dict>
			<key>hash2</key>
			<data>
			cf3eMSfGjMwelyl8BcbWxdVTKGFgulmJy9TGB5k7GPg=
			</data>
		</dict>
		<key>Resources/TrayIconTemplate-Dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			eYo5ExupRrRqPV38UWvXW1GAGwxBQGjBs9kvx3nkwmU=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			Lv975in5tFjyOGfKFE0cQkj2Rx+lcoXGqvVlBSJPYn4=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			L0mlQOmX5X7zzuvFW8MlsvYmUbLcxI03U+8x+wYC4qQ=
			</data>
		</dict>
		<key>Resources/TrayIconTemplate.png</key>
		<dict>
			<key>hash2</key>
			<data>
			JAeDz5eBbgAA5SSuUxinUo6dmClqrCTQjG4l+Uv00Oo=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			RfmQkdeQecIAUvzhqNeWNlYUWGRo/DrOajYYwWZTjlI=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			6z+qH/r4jPZpKpjntgjNSBotIC6rCW2dye/YnbrGBZc=
			</data>
		</dict>
		<key>Resources/app.asar</key>
		<dict>
			<key>hash2</key>
			<data>
			Swkf+WDRWsLWWLD1r1YKSEzvMHnM2NgcCGC2D8IvfH4=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/claude-native/artifacts/claude-native.darwin-arm64.node</key>
		<dict>
			<key>hash2</key>
			<data>
			LITQB5ETY32TyuhjOoVRJWwWNx4JE1fP5HDCs68IrJE=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/claude-native/artifacts/claude-native.darwin-x64.node</key>
		<dict>
			<key>hash2</key>
			<data>
			1s7rb8J2vSykb5H+8twNaHhqAqvDMECThOGlMvwXErE=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/claude-native/claude-native-binding.node</key>
		<dict>
			<key>hash2</key>
			<data>
			ei5KetY4iN03dUeaWebW0nbvgg8izrM+KOq3Ktl9QQ4=
			</data>
		</dict>
		<key>Resources/build-props.json</key>
		<dict>
			<key>hash2</key>
			<data>
			HJxQeuDQtbH63SiLPH44Ccu+5Mc3hyL/GrQWDq/3aIg=
			</data>
		</dict>
		<key>Resources/de-DE.json</key>
		<dict>
			<key>hash2</key>
			<data>
			y6W/7UOvO7y3OI/JcwB+JdEXS5rxGW5rpp4zzKu2gWc=
			</data>
		</dict>
		<key>Resources/electron.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			fQ/7wpuC1H7GOWL2DW3LjAZZxilZoZ1lAZEAbg2EzCc=
			</data>
		</dict>
		<key>Resources/en-US.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FxbrkBIV0AvX/0LEDY/rLm5bXHeNTKim1KvWRjU3lg0=
			</data>
		</dict>
		<key>Resources/en-XA.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uUmJWami5w4O+mPShfBa28VNkf+Ne7f9aDo9RuK7u7I=
			</data>
		</dict>
		<key>Resources/en-XB.json</key>
		<dict>
			<key>hash2</key>
			<data>
			dv6iMhzFxFnC2AIfwN4z+H05+XEWM9nXgM+SQTnJ0Eo=
			</data>
		</dict>
		<key>Resources/es-419.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Zgkxs6ZPq+7QZIvDHsm/EPUNLnN6B7qQx+xZZbbW7Ss=
			</data>
		</dict>
		<key>Resources/es-ES.json</key>
		<dict>
			<key>hash2</key>
			<data>
			fTjJyePV03jLiTYtwEsQxuDaa2FVLHE5Zi7gX1EB9oU=
			</data>
		</dict>
		<key>Resources/fr-FR.json</key>
		<dict>
			<key>hash2</key>
			<data>
			qTaBWJR0Fx6KmxCZrp8gFDAutFiMEPEGWCkHc3g8FUc=
			</data>
		</dict>
		<key>Resources/hi-IN.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Zx+sHt7cUaxxpXsZVhH+hK3lVDq47l5YhN53xFy2XxU=
			</data>
		</dict>
		<key>Resources/id-ID.json</key>
		<dict>
			<key>hash2</key>
			<data>
			RYEzRqcZlxeRh+J+x/fG2tzjdsf2BE0Is0OXAvBCtLM=
			</data>
		</dict>
		<key>Resources/it-IT.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8Fcx52MDWm+asW4cCgIlrW1duIJ8yMKQrbUNpPrqmKY=
			</data>
		</dict>
		<key>Resources/ja-JP.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7GM8THiB7p9zrzOhIK1Cca7+tnQTpOi/x5PZP0Ism8s=
			</data>
		</dict>
		<key>Resources/ko-KR.json</key>
		<dict>
			<key>hash2</key>
			<data>
			K4iwF8AYvGDfMrJg90nLF9wXgRrEg7R/0rrvbLehUaM=
			</data>
		</dict>
		<key>Resources/pt-BR.json</key>
		<dict>
			<key>hash2</key>
			<data>
			AFV7wytgFwSG0YlkE2346c4zEvQ5NUKpdOgsDs5aI6U=
			</data>
		</dict>
		<key>Resources/xx-AC.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+SDMf/RgmsaAMJvs2SUGEq0btfBN+LeuwB9/ePqZBfY=
			</data>
		</dict>
		<key>Resources/xx-HA.json</key>
		<dict>
			<key>hash2</key>
			<data>
			dGTQgXVXykjtbUlosWP/EcfbBf89m0aeA6UTO6HWdpY=
			</data>
		</dict>
		<key>Resources/xx-LS.json</key>
		<dict>
			<key>hash2</key>
			<data>
			nFH+xM0L+RMY2U7n/l444RaNXlA+o3Hkc15jkAiXIkA=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
