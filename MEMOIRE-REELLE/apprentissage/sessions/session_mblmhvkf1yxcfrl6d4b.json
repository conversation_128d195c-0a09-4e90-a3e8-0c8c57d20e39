{"id": "mblmhvkf1yxcfrl6d4b", "type": "reconnaissance_patterns", "debut": "2025-06-07T02:36:31.119Z", "duree_prevue": 180000, "qi_debut": 155, "objectifs": ["<PERSON><PERSON><PERSON><PERSON> précision", "Augmenter vitesse", "Détecter patterns complexes"], "progres": [{"etape": 3, "type": "pattern_reconnu", "difficulte": 3, "precision": 72.47875808831002}, {"etape": 7, "type": "pattern_reconnu", "difficulte": 5, "precision": 73.53835689846846}, {"etape": 9, "type": "pattern_reconnu", "difficulte": 3, "precision": 94.10806210454518}], "fin": "2025-06-07T02:36:32.131Z", "duree_reelle": 1012, "qi_fin": 157, "gain_qi": 2}