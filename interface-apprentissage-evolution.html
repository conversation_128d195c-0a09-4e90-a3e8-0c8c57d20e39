<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LOUNA-AI - Apprentissage et Évolution</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            margin: 0;
            padding: 20px;
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .title {
            font-size: 3.5em;
            font-weight: bold;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #FFD700, #FFA500, #FF6B6B, #4ECDC4, #45B7D1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradient 4s ease-in-out infinite;
        }
        @keyframes gradient {
            0%, 100% { filter: hue-rotate(0deg); }
            25% { filter: hue-rotate(90deg); }
            50% { filter: hue-rotate(180deg); }
            75% { filter: hue-rotate(270deg); }
        }
        
        /* QI EVOLUTION */
        .qi-evolution {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }
        .qi-display {
            font-size: 4em;
            font-weight: bold;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .qi-progress {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            height: 30px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        .qi-fill {
            height: 100%;
            background: linear-gradient(45deg, #4ECDC4, #44A08D, #FFD700);
            border-radius: 25px;
            transition: width 2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .qi-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .qi-stat {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        .qi-stat-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #4ECDC4;
        }
        
        /* MODULES APPRENTISSAGE */
        .modules-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        .modules-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #FFD700;
        }
        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .module-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        .module-card:hover {
            transform: translateY(-5px);
            border-color: #4ECDC4;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        .module-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .module-name {
            font-size: 1.2em;
            font-weight: bold;
        }
        .module-level {
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-weight: bold;
        }
        .module-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        .module-metric {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 10px;
            text-align: center;
        }
        .module-metric-value {
            font-size: 1.3em;
            font-weight: bold;
            color: #FFD700;
        }
        
        /* SESSIONS */
        .sessions-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        .sessions-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #FFD700;
        }
        .session-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .session-btn {
            padding: 15px 20px;
            border: none;
            border-radius: 15px;
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1em;
        }
        .session-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        .session-btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        .session-btn.active {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: black;
        }
        
        /* PROGRESSION */
        .progression-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        .progression-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #FFD700;
        }
        .progression-chart {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
            color: #4ECDC4;
        }
        .progression-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .progression-metric {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        .progression-value {
            font-size: 2em;
            font-weight: bold;
            color: #4ECDC4;
            margin-bottom: 5px;
        }
        
        /* LOGS */
        .logs-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 25px;
            max-height: 300px;
            overflow-y: auto;
        }
        .logs-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #FFD700;
        }
        .log-entry {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 10px 15px;
            margin-bottom: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .log-entry.learning { border-left: 4px solid #4ECDC4; }
        .log-entry.evolution { border-left: 4px solid #FFD700; }
        .log-entry.success { border-left: 4px solid #4CAF50; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .pulse { animation: pulse 2s infinite; }
    </style>
</head>
<body>
    <!-- NAVIGATION GLOBALE -->
    <nav style="position: fixed; top: 0; left: 0; right: 0; background: linear-gradient(135deg, rgba(0,0,0,0.9) 0%, rgba(30,30,30,0.95) 100%); backdrop-filter: blur(15px); border-bottom: 2px solid rgba(255,255,255,0.1); z-index: 1000; padding: 10px 0;">
        <div style="max-width: 1400px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center; padding: 0 20px; flex-wrap: wrap;">
            <a href="/" style="font-size: 1.5em; font-weight: bold; background: linear-gradient(45deg, #3498db, #2ecc71, #f39c12, #e74c3c); -webkit-background-clip: text; -webkit-text-fill-color: transparent; text-decoration: none;">🧠 LOUNA-AI</a>

            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                <a href="/" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.1); transition: all 0.3s ease; font-size: 0.9em;">🏠 Accueil</a>
                <a href="/agent" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.1); transition: all 0.3s ease; font-size: 0.9em;">🤖 Agent</a>
                <a href="/apprentissage" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: linear-gradient(45deg, #3498db, #2ecc71); transition: all 0.3s ease; font-size: 0.9em;">📚 Apprentissage</a>
                <a href="/cerveau" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.1); transition: all 0.3s ease; font-size: 0.9em;">🧠 Cerveau</a>
                <a href="/memoire" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.1); transition: all 0.3s ease; font-size: 0.9em;">🔥 Mémoire</a>
                <a href="/pensees" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.1); transition: all 0.3s ease; font-size: 0.9em;">👁️ Pensées</a>
                <a href="/chat" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.1); transition: all 0.3s ease; font-size: 0.9em;">💬 Chat</a>
                <a href="/dashboard" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.1); transition: all 0.3s ease; font-size: 0.9em;">📊 Dashboard</a>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="header">
            <div class="title">🧠 Apprentissage & Évolution</div>
            <div class="subtitle">Système d'Apprentissage Avancé LOUNA-AI</div>
        </div>

        <!-- QI EVOLUTION -->
        <div class="qi-evolution">
            <div class="qi-display pulse" id="qi-display">126</div>
            <div style="font-size: 1.2em; margin-bottom: 20px;">QI Actuel / Maximum</div>
            
            <div class="qi-progress">
                <div class="qi-fill" id="qi-fill" style="width: 25.2%;">25.2%</div>
            </div>
            
            <div class="qi-stats">
                <div class="qi-stat">
                    <div class="qi-stat-value" id="qi-initial">76</div>
                    <div>QI Initial</div>
                </div>
                <div class="qi-stat">
                    <div class="qi-stat-value" id="qi-gain">+50</div>
                    <div>Gain Total</div>
                </div>
                <div class="qi-stat">
                    <div class="qi-stat-value" id="qi-maximum">500</div>
                    <div>QI Maximum</div>
                </div>
                <div class="qi-stat">
                    <div class="qi-stat-value" id="niveau-apprentissage">1</div>
                    <div>Niveau</div>
                </div>
            </div>
        </div>

        <!-- MODULES APPRENTISSAGE -->
        <div class="modules-section">
            <div class="modules-title">📚 Modules d'Apprentissage</div>
            <div class="modules-grid" id="modules-grid">
                <!-- Sera rempli dynamiquement -->
            </div>
        </div>

        <!-- SESSIONS -->
        <div class="sessions-section">
            <div class="sessions-title">🎯 Sessions d'Apprentissage</div>
            <div class="session-controls">
                <button class="session-btn" onclick="demarrerSession('reconnaissance_patterns')">
                    🔍 Reconnaissance Patterns
                </button>
                <button class="session-btn" onclick="demarrerSession('traitement_langage')">
                    💬 Traitement Langage
                </button>
                <button class="session-btn" onclick="demarrerSession('resolution_problemes')">
                    🧩 Résolution Problèmes
                </button>
                <button class="session-btn" onclick="demarrerSession('creativite')">
                    🎨 Créativité
                </button>
                <button class="session-btn" onclick="demarrerSession('apprentissage_adaptatif')">
                    🔄 Apprentissage Adaptatif
                </button>
                <button class="session-btn" onclick="demarrerSession('general')">
                    🌟 Session Générale
                </button>
            </div>
            
            <div style="text-align: center; margin-top: 20px;">
                <div id="session-status" style="font-size: 1.1em; color: #4ECDC4;">
                    Prêt pour une nouvelle session d'apprentissage
                </div>
            </div>
        </div>

        <!-- PROGRESSION -->
        <div class="progression-section">
            <div class="progression-title">📈 Progression et Métriques</div>
            
            <div class="progression-chart" id="progression-chart">
                📊 Graphique de progression QI (en développement)
            </div>
            
            <div class="progression-metrics">
                <div class="progression-metric">
                    <div class="progression-value" id="sessions-completees">0</div>
                    <div>Sessions Complétées</div>
                </div>
                <div class="progression-metric">
                    <div class="progression-value" id="experience-totale">0</div>
                    <div>Expérience Totale</div>
                </div>
                <div class="progression-metric">
                    <div class="progression-value" id="temps-apprentissage">0h</div>
                    <div>Temps d'Apprentissage</div>
                </div>
                <div class="progression-metric">
                    <div class="progression-value" id="innovations-creees">0</div>
                    <div>Innovations Créées</div>
                </div>
            </div>
        </div>

        <!-- LOGS -->
        <div class="logs-section">
            <div class="logs-title">📋 Logs d'Apprentissage</div>
            <div id="logs-container">
                <div class="log-entry learning">
                    [${new Date().toLocaleTimeString()}] Interface d'apprentissage initialisée
                </div>
            </div>
        </div>
    </div>

    <script>
        let updateInterval;
        let sessionEnCours = false;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            actualiserDonnees();
            updateInterval = setInterval(actualiserDonnees, 15000); // Mise à jour toutes les 15s
        });

        // Actualiser les données
        async function actualiserDonnees() {
            try {
                const response = await fetch('/api/apprentissage/status');
                const data = await response.json();
                
                if (data.success) {
                    updateInterface(data.apprentissage);
                    addLog('learning', 'Données d\'apprentissage mises à jour');
                } else {
                    addLog('error', 'Erreur récupération données: ' + data.error);
                }
            } catch (error) {
                addLog('error', 'Erreur connexion API: ' + error.message);
            }
        }

        // Mettre à jour l'interface
        function updateInterface(data) {
            if (!data || !data.agent) return;
            
            // Mettre à jour QI
            const qiActuel = data.agent.qi_actuel;
            const qiMaximum = data.agent.qi_maximum;
            const qiInitial = data.agent.qi_initial;
            const pourcentage = ((qiActuel - qiInitial) / (qiMaximum - qiInitial)) * 100;
            
            document.getElementById('qi-display').textContent = qiActuel;
            document.getElementById('qi-initial').textContent = qiInitial;
            document.getElementById('qi-gain').textContent = '+' + (qiActuel - qiInitial);
            document.getElementById('qi-maximum').textContent = qiMaximum;
            document.getElementById('niveau-apprentissage').textContent = data.agent.niveau_apprentissage;
            
            const qiFill = document.getElementById('qi-fill');
            qiFill.style.width = pourcentage + '%';
            qiFill.textContent = pourcentage.toFixed(1) + '%';
            
            // Mettre à jour les modules
            updateModules(data.modules_apprentissage);
            
            // Mettre à jour les métriques
            if (data.metriques) {
                document.getElementById('sessions-completees').textContent = data.metriques.sessions_completees;
                document.getElementById('experience-totale').textContent = data.agent.experience_totale.toLocaleString();
                document.getElementById('temps-apprentissage').textContent = 
                    Math.round(data.metriques.temps_apprentissage_total / 3600000) + 'h';
                document.getElementById('innovations-creees').textContent = 
                    data.metriques.innovations_creees.length;
            }
        }

        // Mettre à jour les modules
        function updateModules(modules) {
            const container = document.getElementById('modules-grid');
            container.innerHTML = '';
            
            const noms = {
                reconnaissance_patterns: '🔍 Reconnaissance Patterns',
                traitement_langage: '💬 Traitement Langage',
                resolution_problemes: '🧩 Résolution Problèmes',
                creativite: '🎨 Créativité',
                apprentissage_adaptatif: '🔄 Apprentissage Adaptatif'
            };
            
            Object.entries(modules).forEach(([key, module]) => {
                const card = document.createElement('div');
                card.className = 'module-card';
                
                const metriques = getModuleMetrics(key, module);
                
                card.innerHTML = `
                    <div class="module-header">
                        <div class="module-name">${noms[key] || key}</div>
                        <div class="module-level">Niv. ${module.niveau}</div>
                    </div>
                    <div class="module-metrics">
                        <div class="module-metric">
                            <div class="module-metric-value">${metriques.metric1.value}</div>
                            <div>${metriques.metric1.label}</div>
                        </div>
                        <div class="module-metric">
                            <div class="module-metric-value">${metriques.metric2.value}</div>
                            <div>${metriques.metric2.label}</div>
                        </div>
                    </div>
                `;
                
                container.appendChild(card);
            });
        }

        // Obtenir métriques spécifiques au module
        function getModuleMetrics(key, module) {
            const metriques = {
                reconnaissance_patterns: {
                    metric1: { value: module.precision?.toFixed(1) + '%' || 'N/A', label: 'Précision' },
                    metric2: { value: module.vitesse?.toFixed(0) + '%' || 'N/A', label: 'Vitesse' }
                },
                traitement_langage: {
                    metric1: { value: module.vocabulaire?.toLocaleString() || 'N/A', label: 'Vocabulaire' },
                    metric2: { value: module.comprehension?.toFixed(1) + '%' || 'N/A', label: 'Compréhension' }
                },
                resolution_problemes: {
                    metric1: { value: module.complexite_max || 'N/A', label: 'Complexité Max' },
                    metric2: { value: module.taux_reussite?.toFixed(1) + '%' || 'N/A', label: 'Taux Réussite' }
                },
                creativite: {
                    metric1: { value: module.originalite?.toFixed(1) + '%' || 'N/A', label: 'Originalité' },
                    metric2: { value: module.innovation?.toFixed(1) + '%' || 'N/A', label: 'Innovation' }
                },
                apprentissage_adaptatif: {
                    metric1: { value: module.vitesse_adaptation?.toFixed(1) + '%' || 'N/A', label: 'Adaptation' },
                    metric2: { value: module.retention?.toFixed(1) + '%' || 'N/A', label: 'Rétention' }
                }
            };
            
            return metriques[key] || {
                metric1: { value: 'N/A', label: 'Métrique 1' },
                metric2: { value: 'N/A', label: 'Métrique 2' }
            };
        }

        // Démarrer une session d'apprentissage
        async function demarrerSession(type) {
            if (sessionEnCours) {
                addLog('warning', 'Session déjà en cours...');
                return;
            }
            
            sessionEnCours = true;
            document.getElementById('session-status').textContent = `Session ${type} en cours...`;
            addLog('learning', `Démarrage session: ${type}`);
            
            // Désactiver tous les boutons
            document.querySelectorAll('.session-btn').forEach(btn => btn.disabled = true);
            
            try {
                const response = await fetch('/api/apprentissage/session', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ type: type, duree: 180000 }) // 3 minutes
                });
                
                const data = await response.json();
                
                if (data.success) {
                    addLog('success', `Session ${type} terminée avec succès`);
                    if (data.session && data.session.gain_qi > 0) {
                        addLog('evolution', `QI augmenté de +${data.session.gain_qi}`);
                    }
                    actualiserDonnees(); // Mettre à jour immédiatement
                } else {
                    addLog('error', 'Erreur session: ' + data.error);
                }
            } catch (error) {
                addLog('error', 'Erreur démarrage session: ' + error.message);
            }
            
            sessionEnCours = false;
            document.getElementById('session-status').textContent = 'Prêt pour une nouvelle session d\'apprentissage';
            
            // Réactiver les boutons
            document.querySelectorAll('.session-btn').forEach(btn => btn.disabled = false);
        }

        // Ajouter un log
        function addLog(type, message) {
            const container = document.getElementById('logs-container');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            
            container.insertBefore(entry, container.firstChild);
            
            // Limiter à 50 logs
            while (container.children.length > 50) {
                container.removeChild(container.lastChild);
            }
        }
    </script>
</body>
</html>
