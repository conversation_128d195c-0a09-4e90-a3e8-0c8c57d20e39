/**
 * TEST COMPLET DU SCANNER LOUNA-AI
 * Teste toutes les capacités de scan et navigation
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const os = require('os');

class TestScannerComplet {
    constructor() {
        console.log('🧪 DÉMARRAGE TEST COMPLET SCANNER LOUNA-AI');
        console.log('==========================================');
        
        this.resultats = {
            applications: [],
            fichiers_bureau: [],
            processus_actifs: [],
            informations_systeme: {},
            capacites_navigation: {},
            score_global: 0
        };
    }
    
    // TEST 1: Scanner toutes les applications
    async testerScannerApplications() {
        console.log('\n🔍 TEST 1: SCANNER APPLICATIONS');
        console.log('================================');
        
        try {
            // Scanner les applications macOS
            const applicationsPath = '/Applications';
            const applications = [];
            
            if (fs.existsSync(applicationsPath)) {
                const items = fs.readdirSync(applicationsPath);
                
                items.forEach(item => {
                    if (item.endsWith('.app')) {
                        const appName = item.replace('.app', '');
                        applications.push({
                            nom: appName,
                            chemin: path.join(applicationsPath, item),
                            type: 'Application',
                            integrable: this.estIntegrable(appName)
                        });
                    }
                });
            }
            
            // Scanner les utilitaires système
            const utilitairesPath = '/System/Applications/Utilities';
            if (fs.existsSync(utilitairesPath)) {
                const utils = fs.readdirSync(utilitairesPath);
                utils.forEach(util => {
                    if (util.endsWith('.app')) {
                        const utilName = util.replace('.app', '');
                        applications.push({
                            nom: utilName,
                            chemin: path.join(utilitairesPath, util),
                            type: 'Utilitaire',
                            integrable: this.estIntegrable(utilName)
                        });
                    }
                });
            }
            
            this.resultats.applications = applications;
            
            console.log(`✅ Applications détectées: ${applications.length}`);
            console.log(`✅ Applications intégrables: ${applications.filter(app => app.integrable).length}`);
            
            // Afficher quelques exemples
            console.log('\n📱 EXEMPLES D\'APPLICATIONS DÉTECTÉES:');
            applications.slice(0, 10).forEach(app => {
                console.log(`   ${app.integrable ? '✅' : '❌'} ${app.nom} (${app.type})`);
            });
            
            return true;
            
        } catch (error) {
            console.log('❌ Erreur scan applications:', error.message);
            return false;
        }
    }
    
    // TEST 2: Scanner le bureau et fichiers
    async testerScannerBureau() {
        console.log('\n🖥️ TEST 2: SCANNER BUREAU ET FICHIERS');
        console.log('====================================');
        
        try {
            const bureauPath = path.join(os.homedir(), 'Desktop');
            const fichiersBureau = [];
            
            if (fs.existsSync(bureauPath)) {
                const items = fs.readdirSync(bureauPath);
                
                items.forEach(item => {
                    const itemPath = path.join(bureauPath, item);
                    const stats = fs.statSync(itemPath);
                    
                    fichiersBureau.push({
                        nom: item,
                        chemin: itemPath,
                        type: stats.isDirectory() ? 'Dossier' : 'Fichier',
                        taille: stats.size,
                        modifie: stats.mtime,
                        extension: path.extname(item)
                    });
                });
            }
            
            this.resultats.fichiers_bureau = fichiersBureau;
            
            console.log(`✅ Éléments sur le bureau: ${fichiersBureau.length}`);
            console.log(`✅ Dossiers: ${fichiersBureau.filter(f => f.type === 'Dossier').length}`);
            console.log(`✅ Fichiers: ${fichiersBureau.filter(f => f.type === 'Fichier').length}`);
            
            // Afficher quelques exemples
            console.log('\n📁 EXEMPLES D\'ÉLÉMENTS DU BUREAU:');
            fichiersBureau.slice(0, 8).forEach(fichier => {
                const taille = fichier.type === 'Fichier' ? ` (${this.formatTaille(fichier.taille)})` : '';
                console.log(`   ${fichier.type === 'Dossier' ? '📁' : '📄'} ${fichier.nom}${taille}`);
            });
            
            return true;
            
        } catch (error) {
            console.log('❌ Erreur scan bureau:', error.message);
            return false;
        }
    }
    
    // TEST 3: Scanner les processus actifs
    async testerScannerProcessus() {
        console.log('\n⚡ TEST 3: SCANNER PROCESSUS ACTIFS');
        console.log('=================================');
        
        try {
            // Utiliser ps pour lister les processus
            const processOutput = execSync('ps aux', { encoding: 'utf8' });
            const lignes = processOutput.split('\n').slice(1); // Ignorer l'en-tête
            
            const processus = [];
            
            lignes.forEach(ligne => {
                if (ligne.trim()) {
                    const colonnes = ligne.trim().split(/\s+/);
                    if (colonnes.length >= 11) {
                        const nom = colonnes.slice(10).join(' ');
                        processus.push({
                            pid: colonnes[1],
                            cpu: colonnes[2],
                            memoire: colonnes[3],
                            nom: nom,
                            utilisateur: colonnes[0]
                        });
                    }
                }
            });
            
            this.resultats.processus_actifs = processus;
            
            console.log(`✅ Processus actifs détectés: ${processus.length}`);
            
            // Processus les plus gourmands en CPU
            const topCPU = processus
                .filter(p => parseFloat(p.cpu) > 0)
                .sort((a, b) => parseFloat(b.cpu) - parseFloat(a.cpu))
                .slice(0, 5);
            
            console.log('\n🔥 TOP 5 PROCESSUS CPU:');
            topCPU.forEach(proc => {
                console.log(`   ⚡ ${proc.nom} - CPU: ${proc.cpu}% - PID: ${proc.pid}`);
            });
            
            return true;
            
        } catch (error) {
            console.log('❌ Erreur scan processus:', error.message);
            return false;
        }
    }
    
    // TEST 4: Informations système
    async testerInformationsSysteme() {
        console.log('\n💻 TEST 4: INFORMATIONS SYSTÈME');
        console.log('==============================');
        
        try {
            const infos = {
                plateforme: os.platform(),
                architecture: os.arch(),
                version: os.release(),
                hostname: os.hostname(),
                uptime: os.uptime(),
                memoire_totale: os.totalmem(),
                memoire_libre: os.freemem(),
                cpus: os.cpus().length,
                modele_cpu: os.cpus()[0]?.model || 'Inconnu',
                repertoire_home: os.homedir(),
                utilisateur: os.userInfo().username
            };
            
            this.resultats.informations_systeme = infos;
            
            console.log(`✅ Plateforme: ${infos.plateforme} ${infos.architecture}`);
            console.log(`✅ Hostname: ${infos.hostname}`);
            console.log(`✅ Utilisateur: ${infos.utilisateur}`);
            console.log(`✅ CPU: ${infos.modele_cpu} (${infos.cpus} cœurs)`);
            console.log(`✅ Mémoire: ${this.formatTaille(infos.memoire_libre)}/${this.formatTaille(infos.memoire_totale)} libre`);
            console.log(`✅ Uptime: ${this.formatUptime(infos.uptime)}`);
            
            return true;
            
        } catch (error) {
            console.log('❌ Erreur infos système:', error.message);
            return false;
        }
    }
    
    // TEST 5: Capacités de navigation
    async testerCapacitesNavigation() {
        console.log('\n🌐 TEST 5: CAPACITÉS DE NAVIGATION');
        console.log('=================================');
        
        try {
            const capacites = {
                peut_lire_fichiers: true,
                peut_lister_dossiers: true,
                peut_executer_commandes: true,
                peut_scanner_applications: true,
                peut_monitorer_processus: true,
                peut_acceder_systeme: true,
                peut_naviguer_arborescence: true
            };
            
            // Tester navigation dans l'arborescence
            const testPaths = [
                os.homedir(),
                '/Applications',
                '/System',
                '/usr/bin'
            ];
            
            console.log('🔍 TEST NAVIGATION ARBORESCENCE:');
            testPaths.forEach(testPath => {
                try {
                    if (fs.existsSync(testPath)) {
                        const items = fs.readdirSync(testPath);
                        console.log(`   ✅ ${testPath} - ${items.length} éléments`);
                    } else {
                        console.log(`   ❌ ${testPath} - Inaccessible`);
                        capacites.peut_naviguer_arborescence = false;
                    }
                } catch (error) {
                    console.log(`   ❌ ${testPath} - Erreur: ${error.message}`);
                    capacites.peut_naviguer_arborescence = false;
                }
            });
            
            this.resultats.capacites_navigation = capacites;
            
            console.log('\n✅ CAPACITÉS CONFIRMÉES:');
            Object.entries(capacites).forEach(([capacite, actif]) => {
                console.log(`   ${actif ? '✅' : '❌'} ${capacite.replace(/_/g, ' ')}`);
            });
            
            return true;
            
        } catch (error) {
            console.log('❌ Erreur test navigation:', error.message);
            return false;
        }
    }
    
    // Calculer le score global
    calculerScoreGlobal() {
        console.log('\n📊 CALCUL SCORE GLOBAL');
        console.log('=====================');
        
        let score = 0;
        let maxScore = 0;
        
        // Applications (25 points)
        maxScore += 25;
        if (this.resultats.applications.length > 0) {
            score += Math.min(25, this.resultats.applications.length);
        }
        
        // Fichiers bureau (15 points)
        maxScore += 15;
        if (this.resultats.fichiers_bureau.length >= 0) {
            score += 15;
        }
        
        // Processus (20 points)
        maxScore += 20;
        if (this.resultats.processus_actifs.length > 0) {
            score += 20;
        }
        
        // Infos système (20 points)
        maxScore += 20;
        if (Object.keys(this.resultats.informations_systeme).length > 0) {
            score += 20;
        }
        
        // Capacités navigation (20 points)
        maxScore += 20;
        const capacitesActives = Object.values(this.resultats.capacites_navigation).filter(c => c).length;
        score += (capacitesActives / 7) * 20;
        
        this.resultats.score_global = Math.round((score / maxScore) * 100);
        
        console.log(`📊 Score final: ${this.resultats.score_global}/100`);
        
        return this.resultats.score_global;
    }
    
    // Exécuter tous les tests
    async executerTousLesTests() {
        console.log('🚀 DÉMARRAGE TESTS COMPLETS LOUNA-AI');
        console.log('====================================');
        
        const tests = [
            { nom: 'Scanner Applications', fonction: () => this.testerScannerApplications() },
            { nom: 'Scanner Bureau', fonction: () => this.testerScannerBureau() },
            { nom: 'Scanner Processus', fonction: () => this.testerScannerProcessus() },
            { nom: 'Informations Système', fonction: () => this.testerInformationsSysteme() },
            { nom: 'Capacités Navigation', fonction: () => this.testerCapacitesNavigation() }
        ];
        
        let testsReussis = 0;
        
        for (const test of tests) {
            try {
                const resultat = await test.fonction();
                if (resultat) {
                    testsReussis++;
                    console.log(`✅ ${test.nom}: RÉUSSI`);
                } else {
                    console.log(`❌ ${test.nom}: ÉCHEC`);
                }
            } catch (error) {
                console.log(`❌ ${test.nom}: ERREUR - ${error.message}`);
            }
        }
        
        // Calculer le score
        this.calculerScoreGlobal();
        
        // Rapport final
        console.log('\n🎯 RAPPORT FINAL');
        console.log('===============');
        console.log(`Tests réussis: ${testsReussis}/${tests.length}`);
        console.log(`Score global: ${this.resultats.score_global}/100`);
        
        if (this.resultats.score_global >= 90) {
            console.log('🏆 EXCELLENT - Votre agent est parfaitement opérationnel !');
        } else if (this.resultats.score_global >= 70) {
            console.log('✅ BON - Votre agent fonctionne bien avec quelques limitations');
        } else {
            console.log('⚠️ MOYEN - Votre agent a des capacités limitées');
        }
        
        return this.resultats;
    }
    
    // Utilitaires
    estIntegrable(nomApp) {
        const appsIntegrables = [
            'Safari', 'Chrome', 'Firefox', 'Terminal', 'TextEdit', 
            'Calculator', 'Calendar', 'Mail', 'Notes', 'Finder',
            'VS Code', 'Xcode', 'Docker', 'Activity Monitor'
        ];
        return appsIntegrables.some(app => nomApp.toLowerCase().includes(app.toLowerCase()));
    }
    
    formatTaille(bytes) {
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        if (bytes === 0) return '0 B';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }
    
    formatUptime(seconds) {
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${days}j ${hours}h ${minutes}m`;
    }
}

// Exécuter les tests
async function main() {
    const testeur = new TestScannerComplet();
    const resultats = await testeur.executerTousLesTests();
    
    // Sauvegarder les résultats
    fs.writeFileSync('resultats-test-scanner.json', JSON.stringify(resultats, null, 2));
    console.log('\n💾 Résultats sauvegardés dans: resultats-test-scanner.json');
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = TestScannerComplet;
