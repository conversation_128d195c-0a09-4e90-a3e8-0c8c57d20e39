{"id": "mbm5sbq5wbnnj5mtuck", "type": "resolution_problemes", "debut": "2025-06-07T11:36:31.325Z", "duree_prevue": 180000, "qi_debut": 208, "objectifs": ["Résoudre problèmes complexes", "Optimiser méthodes", "Innover solutions"], "progres": [{"probleme": "Problème de complexité 1", "complexite": 1, "solution": "approche_systematique", "temps": 249.52384420524757}, {"probleme": "Problème de complexité 2", "complexite": 2, "solution": "approche_systematique", "temps": 719.5947085290054}, {"probleme": "Problème de complexité 3", "complexite": 3, "solution": "approche_systematique", "temps": 520.0980886134154}, {"probleme": "Problème de complexité 4", "complexite": 4, "solution": "approche_systematique", "temps": 954.4782024027376}], "fin": "2025-06-07T11:36:32.535Z", "duree_reelle": 1210, "qi_fin": 212, "gain_qi": 4}