#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

class VerificationMemoireComplete {
    constructor() {
        console.log('🔍 VÉRIFICATION COMPLÈTE MÉMOIRE LOUNA-AI');
        console.log('=========================================');
        console.log('🎯 Diagnostic approfondi du système de mémoire');
        
        this.cheminsMemoirePossibles = [
            './MEMOIRE-THERMIQUE',
            './MEMOIRE-THERMIQUE-REELLE', 
            './MEMOIRE-REELLE',
            './CURSEUR-THERMIQUE-TEST',
            '/Volumes/LounaAI_V3/MEMOIRE-THERMIQUE',
            '/Volumes/ALDO et MIM/AI intelligence artificielle louna v4/MEMOIRE-THERMIQUE'
        ];
        
        this.diagnosticComplet = {
            memoire_trouvee: false,
            chemin_memoire: '',
            fichiers_configuration: [],
            zones_thermiques: {},
            neurones_total: 0,
            synapses_total: 0,
            qi_configure: 0,
            accelerateurs_kyber: 0,
            problemes_detectes: [],
            recommandations: []
        };
    }
    
    async lancerVerificationComplete() {
        console.log('\n🚀 LANCEMENT VÉRIFICATION COMPLÈTE');
        console.log('==================================');
        
        // ÉTAPE 1: Localiser la mémoire
        await this.localiserMemoire();
        
        // ÉTAPE 2: Analyser la configuration
        await this.analyserConfiguration();
        
        // ÉTAPE 3: Vérifier les zones thermiques
        await this.verifierZonesThermiques();
        
        // ÉTAPE 4: Analyser les neurones et synapses
        await this.analyserNeuronesSynapses();
        
        // ÉTAPE 5: Vérifier les accélérateurs
        await this.verifierAccelerateurs();
        
        // ÉTAPE 6: Diagnostiquer les problèmes
        await this.diagnostiquerProblemes();
        
        // ÉTAPE 7: Afficher le rapport complet
        this.afficherRapportComplet();
        
        return this.diagnosticComplet;
    }
    
    async localiserMemoire() {
        console.log('\n📍 LOCALISATION DE LA MÉMOIRE');
        console.log('=============================');
        
        for (const chemin of this.cheminsMemoirePossibles) {
            console.log(`🔍 Vérification: ${chemin}`);
            
            if (fs.existsSync(chemin)) {
                console.log(`✅ TROUVÉ: ${chemin}`);
                this.diagnosticComplet.memoire_trouvee = true;
                this.diagnosticComplet.chemin_memoire = chemin;
                
                // Lister les fichiers
                const fichiers = fs.readdirSync(chemin);
                console.log(`📁 Fichiers (${fichiers.length}):`);
                
                fichiers.forEach(fichier => {
                    const cheminFichier = path.join(chemin, fichier);
                    const stats = fs.statSync(cheminFichier);
                    const taille = stats.isFile() ? `${Math.round(stats.size / 1024)}KB` : 'DIR';
                    console.log(`   📄 ${fichier} (${taille})`);
                    
                    if (fichier.includes('config') || fichier.includes('thermal') || fichier.includes('memory')) {
                        this.diagnosticComplet.fichiers_configuration.push({
                            nom: fichier,
                            chemin: cheminFichier,
                            taille: stats.size
                        });
                    }
                });
                
                break;
            } else {
                console.log(`❌ Non trouvé: ${chemin}`);
            }
        }
        
        if (!this.diagnosticComplet.memoire_trouvee) {
            console.log('🚨 AUCUNE MÉMOIRE THERMIQUE TROUVÉE !');
            this.diagnosticComplet.problemes_detectes.push('Mémoire thermique introuvable');
        }
    }
    
    async analyserConfiguration() {
        console.log('\n⚙️ ANALYSE DE LA CONFIGURATION');
        console.log('==============================');
        
        if (!this.diagnosticComplet.memoire_trouvee) {
            console.log('❌ Impossible d\'analyser - mémoire non trouvée');
            return;
        }
        
        const cheminMemoire = this.diagnosticComplet.chemin_memoire;
        
        // Chercher les fichiers de configuration
        const fichiersConfig = [
            'config-correcte.json',
            'thermal-data-jean-luc.json',
            'config.json',
            'neural-drawer-data.json',
            'brain-evolution-data.json'
        ];
        
        for (const fichierConfig of fichiersConfig) {
            const cheminConfig = path.join(cheminMemoire, fichierConfig);
            
            if (fs.existsSync(cheminConfig)) {
                console.log(`📋 Analyse: ${fichierConfig}`);
                
                try {
                    const contenu = fs.readFileSync(cheminConfig, 'utf8');
                    const config = JSON.parse(contenu);
                    
                    console.log(`✅ Configuration valide`);
                    
                    // Analyser le contenu
                    if (config.specifications) {
                        console.log(`🧠 Neurones: ${config.specifications.neurones?.toLocaleString() || 'Non défini'}`);
                        console.log(`🔗 Synapses: ${config.specifications.synapses?.toLocaleString() || 'Non défini'}`);
                        console.log(`🎯 QI Target: ${config.specifications.qi_target || 'Non défini'}`);
                        
                        this.diagnosticComplet.neurones_total = config.specifications.neurones || 0;
                        this.diagnosticComplet.synapses_total = config.specifications.synapses || 0;
                        this.diagnosticComplet.qi_configure = config.specifications.qi_target || 0;
                    }
                    
                    if (config.zones) {
                        console.log(`🌡️ Zones thermiques: ${Object.keys(config.zones).length}`);
                        this.diagnosticComplet.zones_thermiques = config.zones;
                        
                        for (const [nom, zone] of Object.entries(config.zones)) {
                            console.log(`   🔥 ${nom}: ${zone.temperature}°C, ${zone.capacite} entrées`);
                        }
                    }
                    
                } catch (error) {
                    console.log(`❌ Erreur lecture ${fichierConfig}: ${error.message}`);
                    this.diagnosticComplet.problemes_detectes.push(`Configuration corrompue: ${fichierConfig}`);
                }
            } else {
                console.log(`⚠️ Non trouvé: ${fichierConfig}`);
            }
        }
    }
    
    async verifierZonesThermiques() {
        console.log('\n🌡️ VÉRIFICATION ZONES THERMIQUES');
        console.log('=================================');
        
        if (!this.diagnosticComplet.memoire_trouvee) {
            console.log('❌ Impossible de vérifier - mémoire non trouvée');
            return;
        }
        
        const cheminMemoire = this.diagnosticComplet.chemin_memoire;
        
        // Vérifier les dossiers de zones
        const zonesAttendues = ['zone1_70C', 'zone2_60C', 'zone3_50C', 'zone4_40C', 'zone5_30C', 'zone6_20C'];
        let zonesActives = 0;
        
        for (const zone of zonesAttendues) {
            const cheminZone = path.join(cheminMemoire, zone);
            
            if (fs.existsSync(cheminZone)) {
                console.log(`✅ ${zone}: ACTIVE`);
                zonesActives++;
                
                // Vérifier le contenu
                try {
                    const fichiers = fs.readdirSync(cheminZone);
                    console.log(`   📁 Fichiers: ${fichiers.length}`);
                    
                    if (fichiers.length === 0) {
                        console.log(`   ⚠️ Zone vide !`);
                        this.diagnosticComplet.problemes_detectes.push(`Zone ${zone} vide`);
                    }
                } catch (error) {
                    console.log(`   ❌ Erreur lecture zone: ${error.message}`);
                }
            } else {
                console.log(`❌ ${zone}: MANQUANTE`);
                this.diagnosticComplet.problemes_detectes.push(`Zone manquante: ${zone}`);
            }
        }
        
        console.log(`\n📊 Zones actives: ${zonesActives}/6`);
        
        if (zonesActives < 6) {
            this.diagnosticComplet.problemes_detectes.push(`Seulement ${zonesActives}/6 zones actives`);
        }
    }
    
    async analyserNeuronesSynapses() {
        console.log('\n🧠 ANALYSE NEURONES ET SYNAPSES');
        console.log('===============================');
        
        if (!this.diagnosticComplet.memoire_trouvee) {
            console.log('❌ Impossible d\'analyser - mémoire non trouvée');
            return;
        }
        
        const cheminMemoire = this.diagnosticComplet.chemin_memoire;
        
        // Chercher les fichiers de neurones
        const fichiersNeurones = [
            'neurones-201M.dat',
            'neural-drawer-data.json',
            'brain-evolution-data.json'
        ];
        
        let neuronesDetectes = false;
        
        for (const fichier of fichiersNeurones) {
            const cheminFichier = path.join(cheminMemoire, fichier);
            
            if (fs.existsSync(cheminFichier)) {
                console.log(`🧠 Trouvé: ${fichier}`);
                neuronesDetectes = true;
                
                const stats = fs.statSync(cheminFichier);
                const tailleMB = Math.round(stats.size / 1024 / 1024);
                console.log(`   📊 Taille: ${tailleMB}MB`);
                
                if (fichier.endsWith('.json')) {
                    try {
                        const contenu = fs.readFileSync(cheminFichier, 'utf8');
                        const data = JSON.parse(contenu);
                        
                        if (data.neuronSystem) {
                            console.log(`   🧠 Neurones totaux: ${data.neuronSystem.totalNeurons?.toLocaleString() || 'Non défini'}`);
                            console.log(`   ⚡ Neurones actifs: ${data.neuronSystem.activeNeurons?.toLocaleString() || 'Non défini'}`);
                        }
                        
                        if (data.drawers) {
                            console.log(`   📁 Tiroirs: ${Object.keys(data.drawers).length}`);
                        }
                        
                    } catch (error) {
                        console.log(`   ❌ Erreur lecture: ${error.message}`);
                    }
                }
            } else {
                console.log(`❌ Non trouvé: ${fichier}`);
            }
        }
        
        if (!neuronesDetectes) {
            console.log('🚨 AUCUN FICHIER DE NEURONES TROUVÉ !');
            this.diagnosticComplet.problemes_detectes.push('Fichiers de neurones manquants');
        }
        
        // Vérifier la cohérence des nombres
        if (this.diagnosticComplet.neurones_total > 0) {
            console.log(`\n📊 ANALYSE COHÉRENCE:`);
            console.log(`   🧠 Neurones configurés: ${this.diagnosticComplet.neurones_total.toLocaleString()}`);
            console.log(`   🔗 Synapses configurées: ${this.diagnosticComplet.synapses_total.toLocaleString()}`);
            
            const ratioSynapses = this.diagnosticComplet.synapses_total / this.diagnosticComplet.neurones_total;
            console.log(`   📈 Ratio synapses/neurones: ${ratioSynapses.toFixed(1)}`);
            
            if (ratioSynapses < 5) {
                this.diagnosticComplet.problemes_detectes.push('Ratio synapses/neurones trop faible');
            } else if (ratioSynapses > 15) {
                this.diagnosticComplet.problemes_detectes.push('Ratio synapses/neurones trop élevé');
            } else {
                console.log(`   ✅ Ratio cohérent (5-15)`);
            }
        }
    }
    
    async verifierAccelerateurs() {
        console.log('\n⚡ VÉRIFICATION ACCÉLÉRATEURS KYBER');
        console.log('==================================');
        
        if (!this.diagnosticComplet.memoire_trouvee) {
            console.log('❌ Impossible de vérifier - mémoire non trouvée');
            return;
        }
        
        const cheminMemoire = this.diagnosticComplet.chemin_memoire;
        
        // Chercher les fichiers d'accélérateurs
        const fichiersKyber = [
            'kyber-config.json',
            'kyber-variables-actives.json',
            'accelerateurs-kyber.js'
        ];
        
        let kyberDetecte = false;
        
        for (const fichier of fichiersKyber) {
            const cheminFichier = path.join(cheminMemoire, fichier);
            
            if (fs.existsSync(cheminFichier)) {
                console.log(`⚡ Trouvé: ${fichier}`);
                kyberDetecte = true;
                
                if (fichier.endsWith('.json')) {
                    try {
                        const contenu = fs.readFileSync(cheminFichier, 'utf8');
                        const config = JSON.parse(contenu);
                        
                        if (config.accelerateurs) {
                            const nbAccelerateurs = Object.keys(config.accelerateurs).length;
                            console.log(`   ⚡ Accélérateurs: ${nbAccelerateurs}`);
                            this.diagnosticComplet.accelerateurs_kyber = nbAccelerateurs;
                        }
                        
                    } catch (error) {
                        console.log(`   ❌ Erreur lecture: ${error.message}`);
                    }
                }
            } else {
                console.log(`❌ Non trouvé: ${fichier}`);
            }
        }
        
        if (!kyberDetecte) {
            console.log('🚨 AUCUN ACCÉLÉRATEUR KYBER TROUVÉ !');
            this.diagnosticComplet.problemes_detectes.push('Accélérateurs KYBER manquants');
        }
    }
    
    async diagnostiquerProblemes() {
        console.log('\n🔬 DIAGNOSTIC DES PROBLÈMES');
        console.log('===========================');
        
        // Analyser les problèmes détectés
        if (this.diagnosticComplet.problemes_detectes.length === 0) {
            console.log('✅ Aucun problème majeur détecté');
            return;
        }
        
        console.log(`🚨 ${this.diagnosticComplet.problemes_detectes.length} problème(s) détecté(s):`);
        
        this.diagnosticComplet.problemes_detectes.forEach((probleme, index) => {
            console.log(`   ${index + 1}. ${probleme}`);
        });
        
        // Générer des recommandations
        this.genererRecommandations();
    }
    
    genererRecommandations() {
        console.log('\n💡 RECOMMANDATIONS');
        console.log('==================');
        
        const problemes = this.diagnosticComplet.problemes_detectes;
        
        if (problemes.some(p => p.includes('mémoire thermique introuvable'))) {
            this.diagnosticComplet.recommandations.push('Recréer la structure de mémoire thermique');
            console.log('🔧 1. Recréer la structure de mémoire thermique');
        }
        
        if (problemes.some(p => p.includes('zones'))) {
            this.diagnosticComplet.recommandations.push('Initialiser toutes les zones thermiques');
            console.log('🔧 2. Initialiser toutes les zones thermiques (6 zones)');
        }
        
        if (problemes.some(p => p.includes('neurones'))) {
            this.diagnosticComplet.recommandations.push('Régénérer les fichiers de neurones');
            console.log('🔧 3. Régénérer les fichiers de neurones et synapses');
        }
        
        if (problemes.some(p => p.includes('KYBER'))) {
            this.diagnosticComplet.recommandations.push('Configurer les accélérateurs KYBER');
            console.log('🔧 4. Configurer et activer les accélérateurs KYBER');
        }
        
        if (problemes.some(p => p.includes('configuration'))) {
            this.diagnosticComplet.recommandations.push('Réparer les fichiers de configuration');
            console.log('🔧 5. Réparer les fichiers de configuration corrompus');
        }
        
        // Recommandation générale
        this.diagnosticComplet.recommandations.push('Effectuer une reconstruction complète du système');
        console.log('🔧 6. Effectuer une reconstruction complète du système de mémoire');
    }
    
    afficherRapportComplet() {
        console.log('\n' + '='.repeat(80));
        console.log('📋 RAPPORT COMPLET DE VÉRIFICATION MÉMOIRE');
        console.log('='.repeat(80));
        
        console.log(`\n🎯 ÉTAT GÉNÉRAL:`);
        console.log(`   Mémoire trouvée: ${this.diagnosticComplet.memoire_trouvee ? '✅ OUI' : '❌ NON'}`);
        console.log(`   Chemin: ${this.diagnosticComplet.chemin_memoire || 'Aucun'}`);
        
        console.log(`\n📊 CONFIGURATION:`);
        console.log(`   Neurones: ${this.diagnosticComplet.neurones_total.toLocaleString()}`);
        console.log(`   Synapses: ${this.diagnosticComplet.synapses_total.toLocaleString()}`);
        console.log(`   QI configuré: ${this.diagnosticComplet.qi_configure}`);
        console.log(`   Zones thermiques: ${Object.keys(this.diagnosticComplet.zones_thermiques).length}`);
        console.log(`   Accélérateurs KYBER: ${this.diagnosticComplet.accelerateurs_kyber}`);
        
        console.log(`\n🚨 PROBLÈMES (${this.diagnosticComplet.problemes_detectes.length}):`);
        if (this.diagnosticComplet.problemes_detectes.length === 0) {
            console.log('   ✅ Aucun problème détecté');
        } else {
            this.diagnosticComplet.problemes_detectes.forEach((probleme, index) => {
                console.log(`   ${index + 1}. ${probleme}`);
            });
        }
        
        console.log(`\n💡 RECOMMANDATIONS (${this.diagnosticComplet.recommandations.length}):`);
        this.diagnosticComplet.recommandations.forEach((rec, index) => {
            console.log(`   ${index + 1}. ${rec}`);
        });
        
        console.log(`\n🎯 CONCLUSION:`);
        if (this.diagnosticComplet.problemes_detectes.length === 0) {
            console.log('   🌟 Système de mémoire en bon état');
        } else if (this.diagnosticComplet.problemes_detectes.length <= 2) {
            console.log('   ⚠️ Problèmes mineurs - Réparation simple');
        } else if (this.diagnosticComplet.problemes_detectes.length <= 5) {
            console.log('   🔧 Problèmes modérés - Réparation nécessaire');
        } else {
            console.log('   🚨 Problèmes majeurs - Reconstruction recommandée');
        }
        
        // Sauvegarder le rapport
        const timestamp = Date.now();
        const fichierRapport = `rapport-memoire-${timestamp}.json`;
        
        try {
            fs.writeFileSync(fichierRapport, JSON.stringify(this.diagnosticComplet, null, 2));
            console.log(`\n💾 Rapport sauvegardé: ${fichierRapport}`);
        } catch (error) {
            console.log(`\n⚠️ Erreur sauvegarde rapport: ${error.message}`);
        }
    }
}

// Lancement de la vérification
if (require.main === module) {
    const verification = new VerificationMemoireComplete();
    
    verification.lancerVerificationComplete()
        .then(diagnostic => {
            console.log('\n🎉 VÉRIFICATION TERMINÉE !');
            console.log(`🔍 ${diagnostic.problemes_detectes.length} problème(s) détecté(s)`);
            console.log(`💡 ${diagnostic.recommandations.length} recommandation(s)`);
            process.exit(0);
        })
        .catch(error => {
            console.error('\n❌ Erreur vérification:', error.message);
            process.exit(1);
        });
}

module.exports = VerificationMemoireComplete;
