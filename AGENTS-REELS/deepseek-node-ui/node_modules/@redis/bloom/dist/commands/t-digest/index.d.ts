import { RedisCommandArguments } from '@redis/client/dist/lib/commands';
import * as ADD from './ADD';
import * as BY<PERSON><PERSON><PERSON> from './BYRANK';
import * as BYRE<PERSON>ANK from './BYREVRANK';
import * as CDF from './CDF';
import * as CREATE from './CREATE';
import * as INFO from './INFO';
import * as MAX from './MAX';
import * as MERGE from './MERGE';
import * as MIN from './MIN';
import * as QUANTILE from './QUANTILE';
import * as RANK from './RANK';
import * as RESET from './RESET';
import * as REVRANK from './REVRANK';
import * as TRIMMED_MEAN from './TRIMMED_MEAN';
declare const _default: {
    ADD: typeof ADD;
    add: typeof ADD;
    BYRANK: typeof BYRANK;
    byRank: typeof BYRANK;
    BYREVRANK: typeof BYREVRANK;
    byRevRank: typeof BYREVRANK;
    CDF: typeof CDF;
    cdf: typeof CDF;
    CREATE: typeof CREATE;
    create: typeof CREATE;
    INFO: typeof INFO;
    info: typeof INFO;
    MAX: typeof MAX;
    max: typeof MAX;
    MERGE: typeof MERGE;
    merge: typeof MERGE;
    MIN: typeof MIN;
    min: typeof MIN;
    QUANTILE: typeof QUANTILE;
    quantile: typeof QUANTILE;
    RANK: typeof RANK;
    rank: typeof RANK;
    RESET: typeof RESET;
    reset: typeof RESET;
    REVRANK: typeof REVRANK;
    revRank: typeof REVRANK;
    TRIMMED_MEAN: typeof TRIMMED_MEAN;
    trimmedMean: typeof TRIMMED_MEAN;
};
export default _default;
export interface CompressionOption {
    COMPRESSION?: number;
}
export declare function pushCompressionArgument(args: RedisCommandArguments, options?: CompressionOption): RedisCommandArguments;
export declare function transformDoubleReply(reply: string): number;
export declare function transformDoublesReply(reply: Array<string>): Array<number>;
