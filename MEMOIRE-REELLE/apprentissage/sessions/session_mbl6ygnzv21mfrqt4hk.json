{"id": "mbl6ygnzv21mfrqt4hk", "type": "reconnaissance_patterns", "debut": "2025-06-06T19:21:31.103Z", "duree_prevue": 180000, "qi_debut": 131, "objectifs": ["<PERSON><PERSON><PERSON><PERSON> précision", "Augmenter vitesse", "Détecter patterns complexes"], "progres": [{"etape": 2, "type": "pattern_reconnu", "difficulte": 10, "precision": 96.04036283192188}, {"etape": 3, "type": "pattern_reconnu", "difficulte": 6, "precision": 86.9271947126424}, {"etape": 4, "type": "pattern_reconnu", "difficulte": 10, "precision": 99.54881978130288}, {"etape": 5, "type": "pattern_reconnu", "difficulte": 7, "precision": 75.24759217001538}], "fin": "2025-06-06T19:21:32.443Z", "duree_reelle": 1340, "qi_fin": 133, "gain_qi": 2}