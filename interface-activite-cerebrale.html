<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LOUNA-AI - Activité Cérébrale Complète</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
            margin: 0;
            padding: 20px;
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .title {
            font-size: 3.5em;
            font-weight: bold;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #e74c3c, #f39c12, #f1c40f, #2ecc71, #3498db, #9b59b6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: brainwave 3s ease-in-out infinite;
        }
        @keyframes brainwave {
            0%, 100% { filter: hue-rotate(0deg) brightness(1); }
            25% { filter: hue-rotate(90deg) brightness(1.2); }
            50% { filter: hue-rotate(180deg) brightness(1.1); }
            75% { filter: hue-rotate(270deg) brightness(1.3); }
        }
        
        /* RÉSUMÉ GLOBAL */
        .resume-global {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }
        .resume-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .resume-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        .resume-item:hover {
            border-color: #3498db;
            transform: translateY(-5px);
        }
        .resume-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #3498db, #2ecc71);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .resume-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        /* ZONES THERMIQUES */
        .zones-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        .zones-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #f39c12;
            text-align: center;
        }
        .zones-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        .zone-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border-left: 5px solid;
            transition: all 0.3s ease;
        }
        .zone-card:hover {
            transform: translateX(5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        .zone-card.instant { border-left-color: #e74c3c; }
        .zone-card.short-term { border-left-color: #f39c12; }
        .zone-card.working { border-left-color: #f1c40f; }
        .zone-card.long-term { border-left-color: #2ecc71; }
        .zone-card.emotional { border-left-color: #3498db; }
        .zone-card.creative { border-left-color: #9b59b6; }
        
        .zone-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .zone-name {
            font-size: 1.3em;
            font-weight: bold;
        }
        .zone-temp {
            background: linear-gradient(45deg, #e74c3c, #f39c12);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-weight: bold;
        }
        .zone-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        .zone-metric {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 10px;
            text-align: center;
        }
        .zone-metric-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #3498db;
        }
        .zone-metric-label {
            font-size: 0.9em;
            opacity: 0.8;
            margin-top: 5px;
        }
        
        /* ACTIVITÉS SPÉCIALISÉES */
        .activites-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        .activites-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #2ecc71;
            text-align: center;
        }
        .activites-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .activite-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        .activite-card:hover {
            border-color: #2ecc71;
            transform: scale(1.02);
        }
        .activite-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .activite-name {
            font-size: 1.2em;
            font-weight: bold;
        }
        .activite-pourcentage {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-weight: bold;
        }
        .activite-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        .activite-detail {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 8px;
            text-align: center;
            font-size: 0.9em;
        }
        
        /* ONDES CÉRÉBRALES */
        .ondes-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        .ondes-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #9b59b6;
            text-align: center;
        }
        .ondes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .onde-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .onde-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        .onde-name {
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 10px;
            text-transform: uppercase;
        }
        .onde-frequence {
            font-size: 1.3em;
            color: #9b59b6;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .onde-amplitude {
            font-size: 1.1em;
            color: #3498db;
            margin-bottom: 5px;
        }
        .onde-pourcentage {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        /* PERFORMANCE */
        .performance-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
        }
        .performance-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #e74c3c;
            text-align: center;
        }
        .performance-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        .performance-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }
        .performance-label {
            font-size: 1.1em;
            margin-bottom: 10px;
            opacity: 0.9;
        }
        .performance-bar {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            height: 20px;
            margin-bottom: 10px;
            overflow: hidden;
        }
        .performance-fill {
            height: 100%;
            background: linear-gradient(45deg, #e74c3c, #f39c12);
            border-radius: 10px;
            transition: width 1s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 0.9em;
        }
        .performance-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #e74c3c;
        }
        
        /* ANIMATIONS */
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .pulse { animation: pulse 2s infinite; }
        
        /* RESPONSIVE */
        @media (max-width: 768px) {
            .zones-grid, .activites-grid {
                grid-template-columns: 1fr;
            }
            .resume-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            .title {
                font-size: 2.5em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title pulse">🧠 Activité Cérébrale</div>
            <div class="subtitle">Monitoring Neuronal Complet LOUNA-AI</div>
        </div>

        <!-- RÉSUMÉ GLOBAL -->
        <div class="resume-global">
            <h2 style="margin-top: 0; color: #3498db;">📊 Résumé Global de l'Activité</h2>
            <div class="resume-grid">
                <div class="resume-item">
                    <div class="resume-value" id="neurones-actifs">0</div>
                    <div class="resume-label">Neurones Actifs</div>
                </div>
                <div class="resume-item">
                    <div class="resume-value" id="pourcentage-activite">0%</div>
                    <div class="resume-label">Activité Globale</div>
                </div>
                <div class="resume-item">
                    <div class="resume-value" id="synapses-actives">0</div>
                    <div class="resume-label">Synapses Actives</div>
                </div>
                <div class="resume-item">
                    <div class="resume-value" id="pourcentage-synapses">0%</div>
                    <div class="resume-label">Synapses Actives %</div>
                </div>
                <div class="resume-item">
                    <div class="resume-value" id="flux-neural">0</div>
                    <div class="resume-label">Flux Neural</div>
                </div>
                <div class="resume-item">
                    <div class="resume-value" id="efficacite-globale">0%</div>
                    <div class="resume-label">Efficacité Globale</div>
                </div>
            </div>
        </div>

        <!-- ZONES THERMIQUES -->
        <div class="zones-section">
            <div class="zones-title">🔥 Zones Thermiques Détaillées</div>
            <div class="zones-grid" id="zones-grid">
                <!-- Sera rempli dynamiquement -->
            </div>
        </div>

        <!-- ACTIVITÉS SPÉCIALISÉES -->
        <div class="activites-section">
            <div class="activites-title">⚡ Activités Spécialisées</div>
            <div class="activites-grid" id="activites-grid">
                <!-- Sera rempli dynamiquement -->
            </div>
        </div>

        <!-- ONDES CÉRÉBRALES -->
        <div class="ondes-section">
            <div class="ondes-title">🌊 Ondes Cérébrales</div>
            <div class="ondes-grid" id="ondes-grid">
                <!-- Sera rempli dynamiquement -->
            </div>
        </div>

        <!-- PERFORMANCE -->
        <div class="performance-section">
            <div class="performance-title">🎯 Performance Cérébrale</div>
            <div class="performance-grid" id="performance-grid">
                <!-- Sera rempli dynamiquement -->
            </div>
        </div>
    </div>

    <script>
        let updateInterval;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            actualiserActiviteCerebrale();
            updateInterval = setInterval(actualiserActiviteCerebrale, 3000); // Mise à jour toutes les 3s
        });

        // Actualiser l'activité cérébrale
        async function actualiserActiviteCerebrale() {
            try {
                // Récupérer toutes les données
                const [complete, zones, activites, ondes, performance] = await Promise.all([
                    fetch('/api/activite-cerebrale/complete').then(r => r.json()),
                    fetch('/api/activite-cerebrale/zones').then(r => r.json()),
                    fetch('/api/activite-cerebrale/specialisees').then(r => r.json()),
                    fetch('/api/activite-cerebrale/ondes').then(r => r.json()),
                    fetch('/api/activite-cerebrale/performance').then(r => r.json())
                ]);

                if (complete.success) {
                    updateResumeGlobal(complete.activite_cerebrale.resume);
                }
                if (zones.success) {
                    updateZonesThermiques(zones.zones_thermiques);
                }
                if (activites.success) {
                    updateActivitesSpecialisees(activites.activites_specialisees);
                }
                if (ondes.success) {
                    updateOndesCerebrales(ondes.ondes_cerebrales);
                }
                if (performance.success) {
                    updatePerformance(performance.performance_cerebrale);
                }

            } catch (error) {
                console.error('Erreur actualisation activité cérébrale:', error);
            }
        }

        // Mettre à jour le résumé global
        function updateResumeGlobal(resume) {
            document.getElementById('neurones-actifs').textContent = resume.neurones_actifs;
            document.getElementById('pourcentage-activite').textContent = resume.pourcentage_activite;
            document.getElementById('synapses-actives').textContent = resume.synapses_actives;
            document.getElementById('pourcentage-synapses').textContent = resume.pourcentage_synapses;
            document.getElementById('flux-neural').textContent = resume.flux_neural;
            document.getElementById('efficacite-globale').textContent = resume.efficacite_globale;
        }

        // Mettre à jour les zones thermiques
        function updateZonesThermiques(zones) {
            const container = document.getElementById('zones-grid');
            container.innerHTML = '';

            const zonesConfig = {
                'INSTANT': { class: 'instant', icon: '⚡', nom: 'Instant' },
                'SHORT_TERM': { class: 'short-term', icon: '🔄', nom: 'Court Terme' },
                'WORKING': { class: 'working', icon: '💭', nom: 'Travail' },
                'LONG_TERM': { class: 'long-term', icon: '💾', nom: 'Long Terme' },
                'EMOTIONAL': { class: 'emotional', icon: '❤️', nom: 'Émotionnel' },
                'CREATIVE': { class: 'creative', icon: '🎨', nom: 'Créatif' }
            };

            Object.entries(zones).forEach(([nom, zone]) => {
                const config = zonesConfig[nom] || { class: 'default', icon: '🧠', nom: nom };
                
                const card = document.createElement('div');
                card.className = `zone-card ${config.class}`;
                card.innerHTML = `
                    <div class="zone-header">
                        <div class="zone-name">${config.icon} ${config.nom}</div>
                        <div class="zone-temp">${zone.temperature}</div>
                    </div>
                    <div class="zone-metrics">
                        <div class="zone-metric">
                            <div class="zone-metric-value">${zone.neurones_actifs}</div>
                            <div class="zone-metric-label">Neurones Actifs</div>
                        </div>
                        <div class="zone-metric">
                            <div class="zone-metric-value">${zone.pourcentage_activite}</div>
                            <div class="zone-metric-label">Activité</div>
                        </div>
                        <div class="zone-metric">
                            <div class="zone-metric-value">${zone.synapses_actives}</div>
                            <div class="zone-metric-label">Synapses</div>
                        </div>
                        <div class="zone-metric">
                            <div class="zone-metric-value">${zone.efficacite}</div>
                            <div class="zone-metric-label">Efficacité</div>
                        </div>
                        <div class="zone-metric">
                            <div class="zone-metric-value">${zone.frequence}</div>
                            <div class="zone-metric-label">Fréquence</div>
                        </div>
                        <div class="zone-metric">
                            <div class="zone-metric-value">${zone.type_onde}</div>
                            <div class="zone-metric-label">Type Onde</div>
                        </div>
                    </div>
                `;
                container.appendChild(card);
            });
        }

        // Mettre à jour les activités spécialisées
        function updateActivitesSpecialisees(activites) {
            const container = document.getElementById('activites-grid');
            container.innerHTML = '';

            const activitesConfig = {
                'traitement_langage': { icon: '💬', nom: 'Traitement Langage' },
                'reconnaissance_patterns': { icon: '🔍', nom: 'Reconnaissance Patterns' },
                'resolution_problemes': { icon: '🧩', nom: 'Résolution Problèmes' },
                'creativite': { icon: '🎨', nom: 'Créativité' },
                'apprentissage': { icon: '📚', nom: 'Apprentissage' },
                'memoire': { icon: '💾', nom: 'Mémoire' }
            };

            Object.entries(activites).forEach(([nom, activite]) => {
                const config = activitesConfig[nom] || { icon: '⚡', nom: nom };
                
                const card = document.createElement('div');
                card.className = 'activite-card';
                card.innerHTML = `
                    <div class="activite-header">
                        <div class="activite-name">${config.icon} ${config.nom}</div>
                        <div class="activite-pourcentage">${activite.pourcentage_activite}</div>
                    </div>
                    <div class="activite-details">
                        <div class="activite-detail">
                            <strong>Dédiés:</strong><br>${activite.neurones_dedies}
                        </div>
                        <div class="activite-detail">
                            <strong>Actifs:</strong><br>${activite.neurones_actifs}
                        </div>
                        <div class="activite-detail" style="grid-column: 1 / -1;">
                            <strong>Métrique:</strong> ${activite.metrique_specifique}
                        </div>
                    </div>
                `;
                container.appendChild(card);
            });
        }

        // Mettre à jour les ondes cérébrales
        function updateOndesCerebrales(ondes) {
            const container = document.getElementById('ondes-grid');
            container.innerHTML = '';

            const ondesConfig = {
                'gamma': { color: '#e74c3c' },
                'beta': { color: '#f39c12' },
                'alpha': { color: '#f1c40f' },
                'theta': { color: '#2ecc71' },
                'delta': { color: '#3498db' }
            };

            Object.entries(ondes).forEach(([nom, onde]) => {
                const config = ondesConfig[nom] || { color: '#9b59b6' };
                
                const card = document.createElement('div');
                card.className = 'onde-card';
                card.style.borderLeft = `4px solid ${config.color}`;
                card.innerHTML = `
                    <div class="onde-name" style="color: ${config.color};">${nom}</div>
                    <div class="onde-frequence">${onde.frequence}</div>
                    <div class="onde-amplitude">Amplitude: ${onde.amplitude}</div>
                    <div class="onde-pourcentage">Activité: ${onde.pourcentage}</div>
                `;
                container.appendChild(card);
            });
        }

        // Mettre à jour la performance
        function updatePerformance(performance) {
            const container = document.getElementById('performance-grid');
            container.innerHTML = '';

            const performanceConfig = {
                'vitesse_calcul': { icon: '⚡', nom: 'Vitesse Calcul' },
                'precision_reponses': { icon: '🎯', nom: 'Précision Réponses' },
                'efficacite_energetique': { icon: '🔋', nom: 'Efficacité Énergétique' },
                'stabilite_connexions': { icon: '🔗', nom: 'Stabilité Connexions' },
                'adaptation_contexte': { icon: '🔄', nom: 'Adaptation Contexte' },
                'innovation_creative': { icon: '💡', nom: 'Innovation Créative' }
            };

            Object.entries(performance).forEach(([nom, valeur]) => {
                const config = performanceConfig[nom] || { icon: '📊', nom: nom };
                const pourcentage = parseFloat(valeur.replace('%', ''));
                
                const item = document.createElement('div');
                item.className = 'performance-item';
                item.innerHTML = `
                    <div class="performance-label">${config.icon} ${config.nom}</div>
                    <div class="performance-bar">
                        <div class="performance-fill" style="width: ${pourcentage}%;">${valeur}</div>
                    </div>
                    <div class="performance-value">${valeur}</div>
                `;
                container.appendChild(item);
            });
        }
    </script>
</body>
</html>
