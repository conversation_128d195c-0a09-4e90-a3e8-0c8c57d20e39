#!/bin/bash

# Arrêter <PERSON> s'il est en cours d'exécution
pkill ollama

# Définir le dossier Ollama sur le disque externe
export OLLAMA_HOME=/Volumes/seagate/ollama

# Supprimer les fichiers de configuration
echo "Suppression des fichiers de configuration..."
rm -f /Users/<USER>/Documents/augment-projects/Jarvis/deepseek-node-ui/config-ollama.json

# Démarrer Ollama
ollama serve &

# Attendre que Ollama démarre
sleep 2

echo "Ollama est configuré pour utiliser le dossier: $OLLAMA_HOME"
echo "Vous pouvez maintenant télécharger des modèles avec plus d'espace disponible."

# Démarrer l'application
cd /Users/<USER>/Documents/augment-projects/Jarvis/deepseek-node-ui
cp views/index-ollama.ejs views/index.ejs
node server-ollama.js
