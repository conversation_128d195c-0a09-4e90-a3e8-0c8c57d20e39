#!/usr/bin/env node

/**
 * ACCÉLÉRATEUR RÉPONSES ULTRA-RAPIDE - LOUNA-AI
 * Cache intelligent et prédiction de réponses
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class AccelerateurReponses {
    constructor() {
        this.cache = new Map();
        this.predictions = new Map();
        this.patterns = new Map();
        this.statsCache = {
            hits: 0,
            misses: 0,
            predictions_correctes: 0,
            temps_moyen: 0
        };
        
        this.cheminCache = path.join(__dirname, 'MEMOIRE-REELLE', 'cache-reponses.json');
        this.chargerCache();
        this.demarrerAcceleration();
    }

    // GÉNÉRATION HASH POUR CACHE
    genererHash(question) {
        return crypto.createHash('md5').update(question.toLowerCase().trim()).digest('hex');
    }

    // CACHE INTELLIGENT
    obtenirReponseCache(question) {
        const hash = this.genererHash(question);
        const cached = this.cache.get(hash);
        
        if (cached && this.estValide(cached)) {
            this.statsCache.hits++;
            cached.utilisations++;
            cached.derniere_utilisation = Date.now();
            
            console.log(`⚡ CACHE HIT: ${question.substring(0, 50)}...`);
            return {
                contenu: cached.reponse,
                source: 'cache_accelere',
                temps_reponse: 50, // Ultra-rapide
                confiance: cached.confiance,
                utilisations: cached.utilisations
            };
        }
        
        this.statsCache.misses++;
        return null;
    }

    stockerReponseCache(question, reponse, confiance = 0.8) {
        const hash = this.genererHash(question);
        
        const entree = {
            question: question,
            reponse: reponse,
            hash: hash,
            confiance: confiance,
            date_creation: Date.now(),
            derniere_utilisation: Date.now(),
            utilisations: 1,
            mots_cles: this.extraireMots(question),
            categorie: this.determinerCategorie(question)
        };
        
        this.cache.set(hash, entree);
        this.analyserPattern(question, reponse);
        
        // Sauvegarder périodiquement
        if (this.cache.size % 10 === 0) {
            this.sauvegarderCache();
        }
    }

    // PRÉDICTION INTELLIGENTE
    predireReponse(question) {
        const mots = this.extraireMots(question);
        let meilleurePrediction = null;
        let scoreMax = 0;
        
        for (const [hash, entree] of this.cache.entries()) {
            const score = this.calculerSimilarite(mots, entree.mots_cles);
            
            if (score > 0.7 && score > scoreMax) {
                scoreMax = score;
                meilleurePrediction = {
                    reponse: entree.reponse,
                    confiance: score * entree.confiance,
                    source: 'prediction_intelligente'
                };
            }
        }
        
        if (meilleurePrediction && meilleurePrediction.confiance > 0.6) {
            console.log(`🔮 PRÉDICTION: ${question.substring(0, 50)}... (${(meilleurePrediction.confiance*100).toFixed(1)}%)`);
            this.statsCache.predictions_correctes++;
            return meilleurePrediction;
        }
        
        return null;
    }

    // ANALYSE PATTERNS
    analyserPattern(question, reponse) {
        const categorie = this.determinerCategorie(question);
        
        if (!this.patterns.has(categorie)) {
            this.patterns.set(categorie, {
                questions: [],
                reponses_types: [],
                mots_frequents: new Map(),
                temps_moyen: 0
            });
        }
        
        const pattern = this.patterns.get(categorie);
        pattern.questions.push(question);
        
        // Analyser mots fréquents
        const mots = this.extraireMots(question);
        for (const mot of mots) {
            const count = pattern.mots_frequents.get(mot) || 0;
            pattern.mots_frequents.set(mot, count + 1);
        }
        
        // Garder seulement les 50 dernières
        if (pattern.questions.length > 50) {
            pattern.questions = pattern.questions.slice(-50);
        }
    }

    // UTILITAIRES
    extraireMots(texte) {
        return texte.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(mot => mot.length > 2);
    }

    determinerCategorie(question) {
        const mots = question.toLowerCase();
        
        if (mots.includes('code') || mots.includes('program') || mots.includes('script')) return 'programmation';
        if (mots.includes('calcul') || mots.includes('math') || mots.includes('nombre')) return 'mathematiques';
        if (mots.includes('expliqu') || mots.includes('comment') || mots.includes('pourquoi')) return 'explication';
        if (mots.includes('créer') || mots.includes('faire') || mots.includes('générer')) return 'creation';
        
        return 'general';
    }

    calculerSimilarite(mots1, mots2) {
        const set1 = new Set(mots1);
        const set2 = new Set(mots2);
        const intersection = new Set([...set1].filter(x => set2.has(x)));
        const union = new Set([...set1, ...set2]);
        
        return union.size > 0 ? intersection.size / union.size : 0;
    }

    estValide(entree) {
        const age = Date.now() - entree.date_creation;
        const maxAge = 24 * 60 * 60 * 1000; // 24 heures
        
        return age < maxAge && entree.confiance > 0.5;
    }

    // NETTOYAGE AUTOMATIQUE
    nettoyerCache() {
        let supprimees = 0;
        
        for (const [hash, entree] of this.cache.entries()) {
            if (!this.estValide(entree) || entree.utilisations < 2) {
                this.cache.delete(hash);
                supprimees++;
            }
        }
        
        console.log(`🧹 Cache nettoyé: ${supprimees} entrées supprimées`);
    }

    // PERSISTANCE
    chargerCache() {
        try {
            if (fs.existsSync(this.cheminCache)) {
                const data = JSON.parse(fs.readFileSync(this.cheminCache, 'utf8'));
                
                for (const entree of data.cache || []) {
                    this.cache.set(entree.hash, entree);
                }
                
                this.statsCache = { ...this.statsCache, ...(data.stats || {}) };
                console.log(`💾 Cache chargé: ${this.cache.size} entrées`);
            }
        } catch (error) {
            console.log('⚠️ Erreur chargement cache:', error.message);
        }
    }

    sauvegarderCache() {
        try {
            const data = {
                cache: Array.from(this.cache.values()),
                stats: this.statsCache,
                patterns: Array.from(this.patterns.entries()),
                timestamp: new Date().toISOString()
            };
            
            const dir = path.dirname(this.cheminCache);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
            
            fs.writeFileSync(this.cheminCache, JSON.stringify(data, null, 2));
        } catch (error) {
            console.log('⚠️ Erreur sauvegarde cache:', error.message);
        }
    }

    // MONITORING
    demarrerAcceleration() {
        console.log('⚡ ========================================');
        console.log('🚀 ACCÉLÉRATEUR RÉPONSES DÉMARRÉ');
        console.log('⚡ ========================================');
        
        // Nettoyage automatique toutes les heures
        setInterval(() => {
            this.nettoyerCache();
            this.sauvegarderCache();
        }, 60 * 60 * 1000);
        
        // Stats toutes les 5 minutes
        setInterval(() => {
            this.afficherStats();
        }, 5 * 60 * 1000);
        
        console.log(`💾 Cache: ${this.cache.size} entrées`);
        console.log(`📊 Patterns: ${this.patterns.size} catégories`);
    }

    afficherStats() {
        const total = this.statsCache.hits + this.statsCache.misses;
        const tauxReussite = total > 0 ? (this.statsCache.hits / total * 100).toFixed(1) : 0;
        
        console.log(`📊 Cache: ${tauxReussite}% réussite | ${this.cache.size} entrées | ${this.statsCache.predictions_correctes} prédictions`);
    }

    // API
    obtenirStatistiques() {
        return {
            cache_size: this.cache.size,
            patterns_count: this.patterns.size,
            stats: this.statsCache,
            taux_reussite: this.statsCache.hits + this.statsCache.misses > 0 
                ? (this.statsCache.hits / (this.statsCache.hits + this.statsCache.misses) * 100).toFixed(1)
                : 0
        };
    }

    // MÉTHODES PRINCIPALES
    accelerer(question) {
        // 1. Vérifier cache
        const cached = this.obtenirReponseCache(question);
        if (cached) return cached;
        
        // 2. Essayer prédiction
        const prediction = this.predireReponse(question);
        if (prediction) return prediction;
        
        // 3. Aucune accélération possible
        return null;
    }
}

// Démarrer l'accélérateur
const accelerateur = new AccelerateurReponses();

// Export
module.exports = accelerateur;

// Si lancé directement
if (require.main === module) {
    console.log('🚀 Accélérateur réponses actif...');
    console.log('🛑 Appuyez sur Ctrl+C pour arrêter');
    
    process.on('SIGINT', () => {
        accelerateur.sauvegarderCache();
        console.log('\n🛑 Arrêt accélérateur réponses');
        process.exit(0);
    });
}
