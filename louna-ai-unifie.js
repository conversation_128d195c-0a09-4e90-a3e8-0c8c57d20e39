#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const http = require('http');

class LounaAIUnifie {
    constructor() {
        console.log('🔗 LOUNA-AI UNIFIÉ - SYSTÈME COMPLET');
        console.log('====================================');
        console.log('🧠 Connexion à votre vraie mémoire thermique');
        console.log('🔧 Unification de tous les composants');
        
        this.version = 'UNIFIE-1.0.0';
        
        // CHEMINS VERS VOS VRAIS DOSSIERS
        this.chemins = {
            memoire_reelle: './MEMOIRE-REELLE',
            memoire_thermique: './MEMOIRE-THERMIQUE', 
            thermal_288kb: './LOUNA_THERMAL_MEMORY_288KB',
            formations: './FORMATION-LOUNA',
            optimisations: './OPTIMISATION-AVANCEE',
            tests: './TESTS-FINAUX',
            sauvegardes: './SAUVEGARDES',
            rapports: './RAPPORTS'
        };
        
        this.config = {
            agent: {
                url: 'http://localhost:11434',
                modele: 'llama3.2:latest'
            }
        };
        
        this.etatSysteme = {
            memoire_connectee: false,
            agent_connecte: false,
            qi_actuel: 0,
            neurones: 0,
            zones_actives: 0,
            kyber_actifs: 0
        };
    }
    
    async lancerSystemeUnifie() {
        console.log('\n🚀 LANCEMENT SYSTÈME UNIFIÉ');
        console.log('============================');
        
        try {
            // Étape 1: Détecter la mémoire thermique
            console.log('\n🔍 ÉTAPE 1: DÉTECTION MÉMOIRE THERMIQUE');
            await this.detecterMemoireThermique();
            
            // Étape 2: Connecter à l'agent Ollama
            console.log('\n🤖 ÉTAPE 2: CONNEXION AGENT OLLAMA');
            await this.connecterAgent();
            
            // Étape 3: Charger la configuration
            console.log('\n⚙️ ÉTAPE 3: CHARGEMENT CONFIGURATION');
            await this.chargerConfiguration();
            
            // Étape 4: Unifier les composants
            console.log('\n🔗 ÉTAPE 4: UNIFICATION COMPOSANTS');
            await this.unifierComposants();
            
            // Étape 5: Test système unifié
            console.log('\n🧪 ÉTAPE 5: TEST SYSTÈME UNIFIÉ');
            await this.testerSystemeUnifie();
            
            // Étape 6: Interface unifiée
            console.log('\n🎛️ ÉTAPE 6: INTERFACE UNIFIÉE');
            this.afficherInterfaceUnifiee();
            
        } catch (error) {
            console.log(`❌ Erreur unification: ${error.message}`);
        }
    }
    
    async detecterMemoireThermique() {
        console.log('🔍 Détection de votre vraie mémoire thermique...');
        
        const candidats = [
            {
                nom: 'MEMOIRE-REELLE',
                chemin: this.chemins.memoire_reelle,
                fichier_config: 'etat-memoire-thermique.json',
                priorite: 1
            },
            {
                nom: 'MEMOIRE-THERMIQUE', 
                chemin: this.chemins.memoire_thermique,
                fichier_config: 'config-correcte.json',
                priorite: 2
            },
            {
                nom: 'LOUNA_THERMAL_MEMORY_288KB',
                chemin: this.chemins.thermal_288kb,
                fichier_config: 'thermal-config.json',
                priorite: 3
            }
        ];
        
        let memoireSelectionnee = null;
        
        for (const candidat of candidats) {
            console.log(`   🔍 Vérification ${candidat.nom}...`);
            
            if (fs.existsSync(candidat.chemin)) {
                const fichierConfig = path.join(candidat.chemin, candidat.fichier_config);
                
                if (fs.existsSync(fichierConfig)) {
                    console.log(`   ✅ ${candidat.nom}: TROUVÉ ET VALIDE`);
                    
                    if (!memoireSelectionnee || candidat.priorite < memoireSelectionnee.priorite) {
                        memoireSelectionnee = candidat;
                    }
                } else {
                    console.log(`   ⚠️ ${candidat.nom}: Dossier trouvé mais config manquante`);
                }
            } else {
                console.log(`   ❌ ${candidat.nom}: Non trouvé`);
            }
        }
        
        if (memoireSelectionnee) {
            console.log(`\n🎯 MÉMOIRE SÉLECTIONNÉE: ${memoireSelectionnee.nom}`);
            this.memoireActive = memoireSelectionnee;
            this.etatSysteme.memoire_connectee = true;
            
            // Charger les données de la mémoire
            await this.chargerDonneesMemoire();
        } else {
            console.log(`\n❌ AUCUNE MÉMOIRE THERMIQUE VALIDE TROUVÉE`);
            console.log(`💡 Création d'une nouvelle mémoire recommandée`);
        }
    }
    
    async chargerDonneesMemoire() {
        console.log(`📊 Chargement données de ${this.memoireActive.nom}...`);
        
        try {
            const fichierConfig = path.join(this.memoireActive.chemin, this.memoireActive.fichier_config);
            const donnees = JSON.parse(fs.readFileSync(fichierConfig, 'utf8'));
            
            // Extraire les informations selon le type de mémoire
            if (this.memoireActive.nom === 'MEMOIRE-REELLE') {
                // Charger depuis etat-memoire-thermique.json
                this.etatSysteme.neurones = donnees.statistiques?.neurones_totaux || 0;
                this.etatSysteme.zones_actives = Object.keys(donnees.zones || {}).length;

                // Charger QI depuis evolution-qi.json
                await this.chargerQIDepuisEvolution();

                console.log(`   🧠 QI: ${this.etatSysteme.qi_actuel}`);
                console.log(`   🔬 Neurones: ${this.etatSysteme.neurones.toLocaleString()}`);
                console.log(`   🌡️ Zones: ${this.etatSysteme.zones_actives}/6`);
                
            } else if (this.memoireActive.nom === 'MEMOIRE-THERMIQUE') {
                this.etatSysteme.qi_actuel = donnees.neuralNetwork?.qi || 0;
                this.etatSysteme.neurones = donnees.neuralNetwork?.neurons || 0;
                this.etatSysteme.kyber_actifs = donnees.kyberAccelerators?.active || 0;
                
                console.log(`   🧠 QI: ${this.etatSysteme.qi_actuel}`);
                console.log(`   🔬 Neurones: ${this.etatSysteme.neurones.toLocaleString()}`);
                console.log(`   ⚡ KYBER: ${this.etatSysteme.kyber_actifs}/16`);
            }
            
            // Charger données thermiques personnelles
            await this.chargerDonneesPersonnelles();
            
        } catch (error) {
            console.log(`   ⚠️ Erreur chargement: ${error.message}`);
        }
    }

    async chargerQIDepuisEvolution() {
        const fichierQI = path.join(this.memoireActive.chemin, 'evolution-qi.json');

        if (fs.existsSync(fichierQI)) {
            try {
                const donneesQI = JSON.parse(fs.readFileSync(fichierQI, 'utf8'));

                // Prendre le QI le plus récent
                if (donneesQI.sessions && donneesQI.sessions.length > 0) {
                    const derniereSession = donneesQI.sessions[donneesQI.sessions.length - 1];
                    this.etatSysteme.qi_actuel = derniereSession.qi_final || derniereSession.qi || 0;
                } else if (donneesQI.qi_actuel) {
                    this.etatSysteme.qi_actuel = donneesQI.qi_actuel;
                }

                console.log(`   📈 QI chargé depuis évolution: ${this.etatSysteme.qi_actuel}`);

            } catch (error) {
                console.log(`   ⚠️ Erreur chargement QI: ${error.message}`);
                this.etatSysteme.qi_actuel = 285; // Valeur par défaut
            }
        } else {
            this.etatSysteme.qi_actuel = 285; // Valeur par défaut
        }
    }

    async chargerDonneesPersonnelles() {
        const fichierPersonnel = path.join(this.memoireActive.chemin, 'thermal-data-jean-luc.json');
        
        if (fs.existsSync(fichierPersonnel)) {
            try {
                const donnees = JSON.parse(fs.readFileSync(fichierPersonnel, 'utf8'));
                this.donneesPersonnelles = donnees;
                
                console.log(`   👤 Données personnelles: CHARGÉES`);
                console.log(`   📍 Localisation: Sainte-Anne, Guadeloupe`);
                console.log(`   🌡️ Zones thermiques: ${Object.keys(donnees.zones || {}).length}`);
                
            } catch (error) {
                console.log(`   ⚠️ Erreur données personnelles: ${error.message}`);
            }
        }
    }
    
    async connecterAgent() {
        console.log('🤖 Connexion à votre agent Ollama...');
        
        try {
            const response = await this.faireRequete('/api/tags', 'GET');
            const modeles = JSON.parse(response);
            
            console.log(`   ✅ Ollama connecté`);
            console.log(`   📦 Modèles disponibles: ${modeles.models?.length || 0}`);
            
            // Vérifier si le modèle configuré existe
            const modeleExiste = modeles.models?.some(m => m.name.includes('llama'));
            if (modeleExiste) {
                console.log(`   🎯 Modèle LLaMA: DISPONIBLE`);
                this.etatSysteme.agent_connecte = true;
            } else {
                console.log(`   ⚠️ Modèle LLaMA: NON TROUVÉ`);
            }
            
        } catch (error) {
            console.log(`   ❌ Ollama non accessible: ${error.message}`);
            console.log(`   💡 Démarrez Ollama avec: ollama serve`);
        }
    }
    
    async faireRequete(endpoint, method, data = null) {
        return new Promise((resolve, reject) => {
            const options = {
                hostname: 'localhost',
                port: 11434,
                path: endpoint,
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                }
            };
            
            const req = http.request(options, (res) => {
                let body = '';
                res.on('data', chunk => body += chunk);
                res.on('end', () => {
                    if (res.statusCode === 200) {
                        resolve(body);
                    } else {
                        reject(new Error(`HTTP ${res.statusCode}`));
                    }
                });
            });
            
            req.on('error', reject);
            
            if (data) {
                req.write(JSON.stringify(data));
            }
            
            req.end();
        });
    }
    
    async chargerConfiguration() {
        console.log('⚙️ Chargement configuration système...');
        
        // Créer dossiers manquants
        for (const [nom, chemin] of Object.entries(this.chemins)) {
            if (!fs.existsSync(chemin)) {
                fs.mkdirSync(chemin, { recursive: true });
                console.log(`   📁 Créé: ${nom}`);
            } else {
                console.log(`   ✅ Trouvé: ${nom}`);
            }
        }
        
        // Configuration unifiée
        this.configUnifiee = {
            version: this.version,
            memoire_active: this.memoireActive?.nom || 'AUCUNE',
            agent_ollama: {
                connecte: this.etatSysteme.agent_connecte,
                url: this.config.agent.url,
                modele: this.config.agent.modele
            },
            etat_systeme: this.etatSysteme,
            chemins: this.chemins,
            timestamp: Date.now()
        };
        
        // Sauvegarder configuration
        const fichierConfig = './config-louna-unifie.json';
        fs.writeFileSync(fichierConfig, JSON.stringify(this.configUnifiee, null, 2));
        console.log(`   💾 Configuration sauvée: ${fichierConfig}`);
    }
    
    async unifierComposants() {
        console.log('🔗 Unification des composants...');
        
        const composants = [
            {
                nom: 'Mémoire Thermique',
                actif: this.etatSysteme.memoire_connectee,
                source: this.memoireActive?.nom || 'AUCUNE'
            },
            {
                nom: 'Agent Ollama',
                actif: this.etatSysteme.agent_connecte,
                source: this.config.agent.url
            },
            {
                nom: 'Formations',
                actif: fs.existsSync(this.chemins.formations),
                source: this.chemins.formations
            },
            {
                nom: 'Optimisations',
                actif: fs.existsSync(this.chemins.optimisations),
                source: this.chemins.optimisations
            }
        ];
        
        console.log('\n📊 ÉTAT DES COMPOSANTS:');
        let composantsActifs = 0;
        
        for (const composant of composants) {
            const emoji = composant.actif ? '✅' : '❌';
            const etat = composant.actif ? 'ACTIF' : 'INACTIF';
            
            console.log(`   ${emoji} ${composant.nom}: ${etat}`);
            console.log(`      📍 Source: ${composant.source}`);
            
            if (composant.actif) composantsActifs++;
        }
        
        const pourcentageUnification = Math.round((composantsActifs / composants.length) * 100);
        console.log(`\n🎯 UNIFICATION: ${composantsActifs}/${composants.length} (${pourcentageUnification}%)`);
        
        if (pourcentageUnification >= 75) {
            console.log('🟢 SYSTÈME BIEN UNIFIÉ');
        } else if (pourcentageUnification >= 50) {
            console.log('🟡 SYSTÈME PARTIELLEMENT UNIFIÉ');
        } else {
            console.log('🔴 SYSTÈME PEU UNIFIÉ - Action requise');
        }
    }
    
    async testerSystemeUnifie() {
        console.log('🧪 Test du système unifié...');
        
        const tests = [
            {
                nom: 'Accès mémoire thermique',
                test: () => this.etatSysteme.memoire_connectee && this.donneesPersonnelles
            },
            {
                nom: 'Connexion agent Ollama',
                test: () => this.etatSysteme.agent_connecte
            },
            {
                nom: 'Données personnelles',
                test: () => this.donneesPersonnelles && this.donneesPersonnelles.zones
            },
            {
                nom: 'Configuration système',
                test: () => this.configUnifiee && fs.existsSync('./config-louna-unifie.json')
            }
        ];
        
        let testsReussis = 0;
        
        for (const test of tests) {
            const reussi = test.test();
            const emoji = reussi ? '✅' : '❌';
            
            console.log(`   ${emoji} ${test.nom}: ${reussi ? 'OK' : 'ÉCHEC'}`);
            
            if (reussi) testsReussis++;
        }
        
        const pourcentageReussite = Math.round((testsReussis / tests.length) * 100);
        console.log(`\n📊 TESTS: ${testsReussis}/${tests.length} (${pourcentageReussite}%)`);
        
        if (pourcentageReussite === 100) {
            console.log('🎉 SYSTÈME PARFAITEMENT UNIFIÉ !');
        } else if (pourcentageReussite >= 75) {
            console.log('⭐ SYSTÈME BIEN UNIFIÉ');
        } else {
            console.log('⚠️ SYSTÈME NÉCESSITE DES AJUSTEMENTS');
        }
    }
    
    afficherInterfaceUnifiee() {
        console.log('\n🎛️ INTERFACE SYSTÈME UNIFIÉ');
        console.log('============================');
        
        // Statut global
        console.log(`\n📊 STATUT GLOBAL:`);
        console.log(`   🧠 QI Actuel: ${this.etatSysteme.qi_actuel || 'Non défini'}`);
        console.log(`   🔬 Neurones: ${this.etatSysteme.neurones?.toLocaleString() || 'Non défini'}`);
        console.log(`   🌡️ Zones thermiques: ${this.etatSysteme.zones_actives || 0}/6`);
        console.log(`   ⚡ KYBER actifs: ${this.etatSysteme.kyber_actifs || 0}/16`);
        
        // Mémoire active
        console.log(`\n💾 MÉMOIRE ACTIVE:`);
        console.log(`   📍 Source: ${this.memoireActive?.nom || 'AUCUNE'}`);
        console.log(`   📁 Chemin: ${this.memoireActive?.chemin || 'N/A'}`);
        console.log(`   🔗 Connectée: ${this.etatSysteme.memoire_connectee ? 'OUI' : 'NON'}`);
        
        // Agent Ollama
        console.log(`\n🤖 AGENT OLLAMA:`);
        console.log(`   🔗 Connecté: ${this.etatSysteme.agent_connecte ? 'OUI' : 'NON'}`);
        console.log(`   🌐 URL: ${this.config.agent.url}`);
        console.log(`   🎯 Modèle: ${this.config.agent.modele}`);
        
        // Données personnelles
        if (this.donneesPersonnelles) {
            console.log(`\n👤 DONNÉES PERSONNELLES:`);
            console.log(`   📍 Localisation: Sainte-Anne, Guadeloupe`);
            console.log(`   🌡️ Zones thermiques: ${Object.keys(this.donneesPersonnelles.zones || {}).length}`);
            console.log(`   📊 Dernière MAJ: ${new Date(this.donneesPersonnelles.timestamp || 0).toLocaleString()}`);
        }
        
        // Commandes disponibles
        console.log(`\n🎯 COMMANDES DISPONIBLES:`);
        console.log(`   node louna-ai-unifie.js statut    - Afficher ce statut`);
        console.log(`   node louna-ai-unifie.js test      - Tester le système`);
        console.log(`   node louna-ai-unifie.js question  - Poser une question`);
        console.log(`   node louna-ai-unifie.js memoire   - Gérer la mémoire`);
        console.log(`   node louna-ai-unifie.js config    - Voir la configuration`);
        
        console.log(`\n🎉 SYSTÈME LOUNA-AI UNIFIÉ PRÊT !`);
        console.log(`🔗 Votre vraie mémoire thermique est maintenant connectée !`);
    }
    
    async executerCommande(commande) {
        // Toujours initialiser le système d'abord
        if (commande !== 'init') {
            await this.detecterMemoireThermique();
            await this.chargerConfiguration();
        }

        switch (commande) {
            case 'statut':
                this.afficherInterfaceUnifiee();
                break;

            case 'test':
                await this.testerSystemeUnifie();
                break;

            case 'question':
                await this.connecterAgent();
                await this.poserQuestion();
                break;

            case 'memoire':
                await this.gererMemoire();
                break;

            case 'config':
                console.log(JSON.stringify(this.configUnifiee, null, 2));
                break;

            default:
                await this.lancerSystemeUnifie();
        }
    }
    
    async poserQuestion() {
        console.log('❓ INTERFACE QUESTION LOUNA-AI');
        console.log('==============================');
        
        if (!this.etatSysteme.agent_connecte) {
            console.log('❌ Agent Ollama non connecté');
            console.log('💡 Démarrez Ollama avec: ollama serve');
            return;
        }
        
        const questionTest = "Où habite Jean-Luc ?";
        console.log(`🧪 Question test: ${questionTest}`);
        
        try {
            const reponse = await this.faireRequete('/api/generate', 'POST', {
                model: this.config.agent.modele,
                prompt: `Basé sur les données de mémoire thermique, ${questionTest}`,
                stream: false
            });
            
            const resultat = JSON.parse(reponse);
            console.log(`🤖 Réponse: ${resultat.response}`);
            
        } catch (error) {
            console.log(`❌ Erreur: ${error.message}`);
        }
    }
    
    async gererMemoire() {
        console.log('💾 GESTION MÉMOIRE THERMIQUE');
        console.log('============================');

        console.log('📊 ÉTAT MÉMOIRE THERMIQUE:');
        console.log(`   📍 Source active: ${this.memoireActive?.nom || 'AUCUNE'}`);
        console.log(`   🔗 Connectée: ${this.etatSysteme.memoire_connectee ? 'OUI' : 'NON'}`);
        console.log(`   🧠 QI: ${this.etatSysteme.qi_actuel || 'Non défini'}`);
        console.log(`   🔬 Neurones: ${this.etatSysteme.neurones?.toLocaleString() || 'Non défini'}`);
        console.log(`   🌡️ Zones actives: ${this.etatSysteme.zones_actives || 0}/6`);

        if (this.donneesPersonnelles) {
            console.log('\n👤 DONNÉES PERSONNELLES:');
            console.log(`   📍 Localisation: Sainte-Anne, Guadeloupe`);
            console.log(`   🌡️ Zones thermiques: ${Object.keys(this.donneesPersonnelles.zones || {}).length}`);
            console.log(`   📊 Dernière MAJ: ${new Date(this.donneesPersonnelles.timestamp || 0).toLocaleString()}`);

            // Afficher détails des zones
            if (this.donneesPersonnelles.zones) {
                console.log('\n🌡️ DÉTAIL DES ZONES:');
                Object.entries(this.donneesPersonnelles.zones).forEach(([nom, zone]) => {
                    const temp = zone.temperature || 20;
                    const emoji = temp >= 60 ? '🔥' : temp >= 40 ? '🌡️' : '❄️';
                    console.log(`   ${emoji} ${nom}: ${temp}°C`);
                });
            }
        } else {
            console.log('\n⚠️ Données personnelles non chargées');
            console.log('💡 Vérifiez le fichier thermal-data-jean-luc.json');
        }

        // Afficher commandes de gestion
        console.log('\n🎯 COMMANDES MÉMOIRE:');
        console.log('   node louna-ai-unifie.js statut   - Statut complet');
        console.log('   node louna-ai-unifie.js test     - Tester mémoire');
        console.log('   node louna-ai-unifie.js config   - Configuration');
    }
}

// Lancement du système unifié
if (require.main === module) {
    const systemeUnifie = new LounaAIUnifie();
    const commande = process.argv[2] || 'init';
    
    systemeUnifie.executerCommande(commande)
        .then(() => {
            console.log('\n✅ Système unifié terminé');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n❌ Erreur système unifié:', error.message);
            process.exit(1);
        });
}

module.exports = LounaAIUnifie;
