<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LOUNA-AI Chat Avancé - Interface Complète</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            margin: 0;
            padding: 20px;
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .title {
            font-size: 3em;
            font-weight: bold;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        /* STATUS BAR */
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-light {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        .status-light.green { background: #4CAF50; box-shadow: 0 0 10px #4CAF50; }
        .status-light.red { background: #F44336; box-shadow: 0 0 10px #F44336; }
        
        /* CHAT INTERFACE */
        .chat-container {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 20px;
            height: 600px;
        }
        .chat-main {
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            overflow: hidden;
        }
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.2);
        }
        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 12px;
            max-width: 80%;
            word-wrap: break-word;
        }
        .message.user {
            background: linear-gradient(45deg, #667eea, #764ba2);
            margin-left: auto;
            text-align: right;
        }
        .message.assistant {
            background: rgba(255, 255, 255, 0.2);
            margin-right: auto;
        }
        .message-time {
            font-size: 0.8em;
            opacity: 0.7;
            margin-top: 5px;
        }
        
        /* INPUT AREA */
        .chat-input {
            display: flex;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            gap: 10px;
        }
        .chat-input input {
            flex: 1;
            padding: 12px 16px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 16px;
        }
        .chat-input input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        .chat-input button {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .chat-input button:hover {
            transform: scale(1.05);
        }
        .chat-input button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        
        /* SIDEBAR */
        .sidebar {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            overflow-y: auto;
        }
        .sidebar-section {
            margin-bottom: 25px;
        }
        .sidebar-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #FFD700;
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        .stat-value {
            font-weight: bold;
            color: #4CAF50;
        }
        
        /* CONTROLS */
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .control-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            cursor: pointer;
            transition: all 0.3s;
        }
        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .control-btn.active {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: black;
        }
        
        /* ANIMATIONS */
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .typing-indicator {
            display: none;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            margin-bottom: 15px;
            max-width: 80%;
        }
        .typing-dots {
            display: flex;
            gap: 4px;
        }
        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #FFD700;
            animation: typing 1.4s infinite;
        }
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }
        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }
        
        /* RESPONSIVE */
        @media (max-width: 768px) {
            .chat-container {
                grid-template-columns: 1fr;
                height: auto;
            }
            .sidebar {
                order: -1;
                max-height: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">🧠 LOUNA-AI Chat Avancé</div>
            <div class="subtitle">Interface de Chat Complète avec Ollama Intégré</div>
        </div>

        <!-- STATUS BAR -->
        <div class="status-bar">
            <div class="status-item">
                <div class="status-light green" id="ollama-light"></div>
                <span>Ollama: <span id="ollama-status">Connecté</span></span>
            </div>
            <div class="status-item">
                <div class="status-light green" id="system-light"></div>
                <span>Système: <span id="system-status">Opérationnel</span></span>
            </div>
            <div class="status-item">
                <span>QI Total: <strong id="qi-total">226</strong></span>
            </div>
            <div class="status-item">
                <span>Temp: <strong id="temperature">52.5°C</strong></span>
            </div>
        </div>

        <!-- CONTROLS -->
        <div class="controls">
            <button class="control-btn active" onclick="setMode('normal')">💬 Normal</button>
            <button class="control-btn" onclick="setMode('creative')">🎨 Créatif</button>
            <button class="control-btn" onclick="setMode('analytical')">🔬 Analytique</button>
            <button class="control-btn" onclick="setMode('emotional')">❤️ Émotionnel</button>
            <button class="control-btn" onclick="clearChat()">🗑️ Effacer</button>
        </div>

        <!-- CHAT INTERFACE -->
        <div class="chat-container">
            <div class="chat-main">
                <div class="chat-messages" id="chat-messages">
                    <div class="message assistant">
                        <div>Bonjour ! Je suis LOUNA-AI avec Ollama intégré. Comment puis-je vous aider aujourd'hui ?</div>
                        <div class="message-time" id="welcome-time"></div>
                    </div>
                </div>
                <div class="typing-indicator" id="typing-indicator">
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
                <div class="chat-input">
                    <input type="text" id="message-input" placeholder="Tapez votre message..." onkeypress="handleKeyPress(event)">
                    <button onclick="sendMessage()" id="send-btn">Envoyer</button>
                </div>
            </div>

            <!-- SIDEBAR -->
            <div class="sidebar">
                <div class="sidebar-section">
                    <div class="sidebar-title">📊 Statistiques</div>
                    <div class="stat-item">
                        <span>Messages envoyés:</span>
                        <span class="stat-value" id="messages-sent">0</span>
                    </div>
                    <div class="stat-item">
                        <span>Temps de réponse:</span>
                        <span class="stat-value" id="response-time">-</span>
                    </div>
                    <div class="stat-item">
                        <span>Mode actuel:</span>
                        <span class="stat-value" id="current-mode">Normal</span>
                    </div>
                </div>

                <div class="sidebar-section">
                    <div class="sidebar-title">🧠 Mémoire Thermique</div>
                    <div class="stat-item">
                        <span>Zone INSTANT:</span>
                        <span class="stat-value">70°C</span>
                    </div>
                    <div class="stat-item">
                        <span>Zone WORKING:</span>
                        <span class="stat-value">50°C</span>
                    </div>
                    <div class="stat-item">
                        <span>Zone CREATIVE:</span>
                        <span class="stat-value">55°C</span>
                    </div>
                </div>

                <div class="sidebar-section">
                    <div class="sidebar-title">⚡ Performance</div>
                    <div class="stat-item">
                        <span>Neurones:</span>
                        <span class="stat-value">201.2M</span>
                    </div>
                    <div class="stat-item">
                        <span>Synapses:</span>
                        <span class="stat-value">1.9B</span>
                    </div>
                    <div class="stat-item">
                        <span>Efficacité:</span>
                        <span class="stat-value" id="efficiency">94%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let messageCount = 0;
        let currentMode = 'normal';

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('welcome-time').textContent = new Date().toLocaleTimeString();
            updateStatus();
            setInterval(updateStatus, 10000); // Mise à jour toutes les 10s
        });

        // Gestion des modes
        function setMode(mode) {
            currentMode = mode;
            document.getElementById('current-mode').textContent = mode.charAt(0).toUpperCase() + mode.slice(1);
            
            // Mettre à jour les boutons
            document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Ajouter un message système
            addMessage('system', `Mode changé vers: ${mode.charAt(0).toUpperCase() + mode.slice(1)}`);
        }

        // Envoi de message
        function sendMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Ajouter le message utilisateur
            addMessage('user', message);
            input.value = '';
            messageCount++;
            document.getElementById('messages-sent').textContent = messageCount;
            
            // Désactiver le bouton et afficher l'indicateur de frappe
            document.getElementById('send-btn').disabled = true;
            document.getElementById('typing-indicator').style.display = 'block';
            
            // Envoyer à Ollama
            sendToOllama(message);
        }

        // Envoi à Ollama
        async function sendToOllama(message) {
            const startTime = Date.now();
            
            try {
                const response = await fetch('/api/test-ollama', {
                    method: 'GET'
                });
                
                const data = await response.json();
                const responseTime = Date.now() - startTime;
                
                // Masquer l'indicateur de frappe
                document.getElementById('typing-indicator').style.display = 'none';
                
                if (data.success && data.test && data.test.reponse) {
                    addMessage('assistant', data.test.reponse);
                } else {
                    addMessage('assistant', 'Désolé, je rencontre des difficultés pour répondre en ce moment.');
                }
                
                // Mettre à jour le temps de réponse
                document.getElementById('response-time').textContent = responseTime + 'ms';
                
            } catch (error) {
                document.getElementById('typing-indicator').style.display = 'none';
                addMessage('assistant', 'Erreur de connexion avec Ollama.');
                console.error('Erreur:', error);
            }
            
            // Réactiver le bouton
            document.getElementById('send-btn').disabled = false;
        }

        // Ajouter un message
        function addMessage(type, content) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.textContent = content;
            
            const timeDiv = document.createElement('div');
            timeDiv.className = 'message-time';
            timeDiv.textContent = new Date().toLocaleTimeString();
            
            messageDiv.appendChild(contentDiv);
            messageDiv.appendChild(timeDiv);
            messagesContainer.appendChild(messageDiv);
            
            // Scroll vers le bas
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Gestion des touches
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // Effacer le chat
        function clearChat() {
            const messagesContainer = document.getElementById('chat-messages');
            messagesContainer.innerHTML = `
                <div class="message assistant">
                    <div>Chat effacé. Comment puis-je vous aider ?</div>
                    <div class="message-time">${new Date().toLocaleTimeString()}</div>
                </div>
            `;
            messageCount = 0;
            document.getElementById('messages-sent').textContent = '0';
        }

        // Mise à jour du statut
        async function updateStatus() {
            try {
                const response = await fetch('/api/status-ultra-complet');
                const data = await response.json();
                
                if (data.success) {
                    // Mettre à jour les indicateurs
                    const ollamaLight = document.getElementById('ollama-light');
                    const ollamaStatus = document.getElementById('ollama-status');
                    
                    if (data.modules && data.modules.ollama && data.modules.ollama.actif) {
                        ollamaLight.className = 'status-light green';
                        ollamaStatus.textContent = 'Connecté';
                    } else {
                        ollamaLight.className = 'status-light red';
                        ollamaStatus.textContent = 'Déconnecté';
                    }
                    
                    // Mettre à jour les stats
                    if (data.qi_total) {
                        document.getElementById('qi-total').textContent = data.qi_total;
                    }
                    
                    if (data.modules && data.modules.memoireThermique) {
                        document.getElementById('temperature').textContent = 
                            data.modules.memoireThermique.temperatureGlobale + '°C';
                    }
                }
            } catch (error) {
                console.error('Erreur mise à jour statut:', error);
            }
        }
    </script>
</body>
</html>
