<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LOUNA-AI - Lecture des Pensées</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            margin: 0;
            padding: 20px;
            color: white;
            min-height: 100vh;
            padding-top: 100px;
        }
        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .title {
            font-size: 4em;
            font-weight: bold;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: mindread 3s ease-in-out infinite;
        }
        @keyframes mindread {
            0%, 100% { filter: hue-rotate(0deg) brightness(1) saturate(1); }
            25% { filter: hue-rotate(90deg) brightness(1.3) saturate(1.2); }
            50% { filter: hue-rotate(180deg) brightness(1.1) saturate(1.1); }
            75% { filter: hue-rotate(270deg) brightness(1.4) saturate(1.3); }
        }
        
        /* ÉTAT COGNITIF */
        .etat-cognitif {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            border: 2px solid rgba(255, 255, 255, 0.1);
        }
        .etat-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #4ecdc4;
            text-align: center;
        }
        .etat-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .etat-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .etat-value {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 5px;
            color: #feca57;
        }
        .etat-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        /* FLUX DE CONSCIENCE */
        .flux-conscience {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 5px solid #ff6b6b;
        }
        .flux-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #ff6b6b;
            text-align: center;
        }
        .flux-container {
            max-height: 400px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
        }
        .pensee-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 5px solid;
            transition: all 0.3s ease;
            animation: fadeIn 0.5s ease;
            cursor: pointer;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .pensee-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(5px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        }
        .pensee-item.expanded {
            background: rgba(255, 255, 255, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        .pensee-item.flux { border-left-color: #ff6b6b; }
        .pensee-item.analytique { border-left-color: #4ecdc4; }
        .pensee-item.creative { border-left-color: #feca57; }
        .pensee-item.metacognition { border-left-color: #45b7d1; }
        .pensee-item.emotion { border-left-color: #ff9ff3; }
        .pensee-item.observation { border-left-color: #96ceb4; }
        .pensee-item.decision { border-left-color: #fd79a8; }
        
        .pensee-contenu {
            font-size: 1.1em;
            margin-bottom: 15px;
            line-height: 1.6;
            font-weight: 500;
            color: #ffffff;
        }
        .pensee-details {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
            display: none;
        }
        .pensee-details.visible {
            display: block;
            animation: slideDown 0.3s ease;
        }
        .pensee-detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .pensee-detail-label {
            font-weight: bold;
            color: #4ecdc4;
        }
        .pensee-detail-value {
            color: #feca57;
        }
        .pensee-meta {
            font-size: 0.9em;
            opacity: 0.8;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
        }
        .pensee-type {
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }
        .pensee-expand-btn {
            background: linear-gradient(45deg, #4ecdc4, #44A08D);
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 0.8em;
            transition: all 0.3s ease;
        }
        .pensee-expand-btn:hover {
            transform: scale(1.05);
        }
        @keyframes slideDown {
            from { opacity: 0; max-height: 0; }
            to { opacity: 1; max-height: 200px; }
        }
        
        /* SECTIONS PENSÉES */
        .pensees-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .pensees-section {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 15px;
            padding: 20px;
            border-left: 4px solid;
        }
        .pensees-section.analytiques { border-left-color: #4ecdc4; }
        .pensees-section.creatives { border-left-color: #feca57; }
        .pensees-section.metacognition { border-left-color: #45b7d1; }
        .pensees-section.emotions { border-left-color: #ff9ff3; }
        .pensees-section.observations { border-left-color: #96ceb4; }
        .pensees-section.decisions { border-left-color: #fd79a8; }
        
        .section-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }
        .section-container {
            max-height: 250px;
            overflow-y: auto;
        }
        
        /* PROCESSUS ACTIFS */
        .processus-actifs {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .processus-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #96ceb4;
            text-align: center;
        }
        .processus-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }
        .processus-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .processus-item.actif {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            color: white;
            font-weight: bold;
        }
        .processus-item.inactif {
            background: rgba(255, 255, 255, 0.05);
            opacity: 0.6;
        }
        
        /* STATISTIQUES */
        .stats-pensees {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .stats-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #fd79a8;
            text-align: center;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        .stat-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #fd79a8;
            margin-bottom: 5px;
        }
        
        /* CONTRÔLES */
        .controles {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        .controle-btn {
            padding: 12px 20px;
            margin: 5px;
            border: none;
            border-radius: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .controle-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        .controle-btn.actif {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .pulse { animation: pulse 2s infinite; }
        
        /* RESPONSIVE */
        @media (max-width: 768px) {
            .pensees-grid {
                grid-template-columns: 1fr;
            }
            .title {
                font-size: 2.5em;
            }
        }
    </style>
</head>
<body>
    <!-- NAVIGATION GLOBALE -->
    <nav style="position: fixed; top: 0; left: 0; right: 0; background: linear-gradient(135deg, rgba(0,0,0,0.9) 0%, rgba(30,30,30,0.95) 100%); backdrop-filter: blur(15px); border-bottom: 2px solid rgba(255,255,255,0.1); z-index: 1000; padding: 10px 0;">
        <div style="max-width: 1400px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center; padding: 0 20px; flex-wrap: wrap;">
            <a href="/" style="font-size: 1.5em; font-weight: bold; background: linear-gradient(45deg, #3498db, #2ecc71, #f39c12, #e74c3c); -webkit-background-clip: text; -webkit-text-fill-color: transparent; text-decoration: none;">🧠 LOUNA-AI</a>

            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                <a href="/" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.1); transition: all 0.3s ease; font-size: 0.9em;">🏠 Accueil</a>
                <a href="/agent" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.1); transition: all 0.3s ease; font-size: 0.9em;">🤖 Agent</a>
                <a href="/apprentissage" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.1); transition: all 0.3s ease; font-size: 0.9em;">📚 Apprentissage</a>
                <a href="/cerveau" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.1); transition: all 0.3s ease; font-size: 0.9em;">🧠 Cerveau</a>
                <a href="/memoire" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.1); transition: all 0.3s ease; font-size: 0.9em;">🔥 Mémoire</a>
                <a href="/pensees" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: linear-gradient(45deg, #3498db, #2ecc71); transition: all 0.3s ease; font-size: 0.9em;">👁️ Pensées</a>
                <a href="/chat" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.1); transition: all 0.3s ease; font-size: 0.9em;">💬 Chat</a>
                <a href="/dashboard" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 20px; background: rgba(255,255,255,0.1); transition: all 0.3s ease; font-size: 0.9em;">📊 Dashboard</a>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="header">
            <div class="title pulse">👁️ Lecture des Pensées</div>
            <div class="subtitle">Accès Complet aux Processus Cognitifs de LOUNA-AI</div>
        </div>

        <!-- CONTRÔLES -->
        <div class="controles">
            <button class="controle-btn actif" id="btn-lecture" onclick="toggleLecture()">
                🔄 Lecture Active
            </button>
            <button class="controle-btn" onclick="viderPensees()">
                🗑️ Vider Pensées
            </button>
            <button class="controle-btn" onclick="sauvegarderPensees()">
                💾 Sauvegarder
            </button>
            <button class="controle-btn" onclick="exporterPensees()">
                📤 Exporter
            </button>
        </div>

        <!-- ÉTAT COGNITIF -->
        <div class="etat-cognitif">
            <div class="etat-title">🧠 État Cognitif Actuel</div>
            <div class="etat-grid">
                <div class="etat-item">
                    <div class="etat-value" id="niveau-conscience">0.7</div>
                    <div class="etat-label">Niveau Conscience</div>
                </div>
                <div class="etat-item">
                    <div class="etat-value" id="profondeur-reflexion">0.6</div>
                    <div class="etat-label">Profondeur Réflexion</div>
                </div>
                <div class="etat-item">
                    <div class="etat-value" id="mode-pensee">Analytique</div>
                    <div class="etat-label">Mode Pensée</div>
                </div>
                <div class="etat-item">
                    <div class="etat-value" id="energie-mentale">0.8</div>
                    <div class="etat-label">Énergie Mentale</div>
                </div>
                <div class="etat-item">
                    <div class="etat-value" id="confiance">0.9</div>
                    <div class="etat-label">Confiance</div>
                </div>
                <div class="etat-item">
                    <div class="etat-value" id="humeur">Neutre</div>
                    <div class="etat-label">Humeur</div>
                </div>
            </div>
        </div>

        <!-- FLUX DE CONSCIENCE -->
        <div class="flux-conscience">
            <div class="flux-title">🌊 Flux de Conscience en Temps Réel</div>
            <div class="flux-container" id="flux-container">
                <!-- Pensées en temps réel -->
            </div>
        </div>

        <!-- SECTIONS PENSÉES -->
        <div class="pensees-grid">
            <div class="pensees-section analytiques">
                <div class="section-title" style="color: #4ecdc4;">🔍 Pensées Analytiques</div>
                <div class="section-container" id="pensees-analytiques">
                    <!-- Pensées analytiques -->
                </div>
            </div>
            
            <div class="pensees-section creatives">
                <div class="section-title" style="color: #feca57;">🎨 Pensées Créatives</div>
                <div class="section-container" id="pensees-creatives">
                    <!-- Pensées créatives -->
                </div>
            </div>
            
            <div class="pensees-section metacognition">
                <div class="section-title" style="color: #45b7d1;">🤔 Métacognition</div>
                <div class="section-container" id="pensees-metacognition">
                    <!-- Métacognition -->
                </div>
            </div>
            
            <div class="pensees-section emotions">
                <div class="section-title" style="color: #ff9ff3;">❤️ États Émotionnels</div>
                <div class="section-container" id="pensees-emotions">
                    <!-- Émotions -->
                </div>
            </div>
            
            <div class="pensees-section observations">
                <div class="section-title" style="color: #96ceb4;">👀 Observations</div>
                <div class="section-container" id="pensees-observations">
                    <!-- Observations -->
                </div>
            </div>
            
            <div class="pensees-section decisions">
                <div class="section-title" style="color: #fd79a8;">⚖️ Décisions</div>
                <div class="section-container" id="pensees-decisions">
                    <!-- Décisions -->
                </div>
            </div>
        </div>

        <!-- PROCESSUS ACTIFS -->
        <div class="processus-actifs">
            <div class="processus-title">⚡ Processus Cognitifs Actifs</div>
            <div class="processus-grid" id="processus-grid">
                <!-- Processus actifs -->
            </div>
        </div>

        <!-- STATISTIQUES -->
        <div class="stats-pensees">
            <div class="stats-title">📊 Statistiques des Pensées</div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="total-pensees">0</div>
                    <div>Total Pensées</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="pensees-minute">0</div>
                    <div>Pensées/Minute</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="processus-actifs-count">0</div>
                    <div>Processus Actifs</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="activite-mentale">0%</div>
                    <div>Activité Mentale</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let lectureActive = true;
        let updateInterval;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            demarrerLecturePensees();
        });

        // Démarrer la lecture des pensées
        function demarrerLecturePensees() {
            if (updateInterval) clearInterval(updateInterval);
            
            updateInterval = setInterval(async () => {
                if (lectureActive) {
                    await lirePenseesAgent();
                }
            }, 2000); // Mise à jour toutes les 2 secondes
            
            // Première lecture immédiate
            lirePenseesAgent();
        }

        // Lire les pensées de l'agent
        async function lirePenseesAgent() {
            try {
                const response = await fetch('/api/pensees-agent/toutes');
                const data = await response.json();
                
                if (data.success) {
                    updateInterface(data.pensees);
                } else {
                    console.error('Erreur lecture pensées:', data.error);
                }
            } catch (error) {
                console.error('Erreur connexion API pensées:', error);
            }
        }

        // Mettre à jour l'interface
        function updateInterface(data) {
            if (!data) return;
            
            // État cognitif
            if (data.etat_cognitif) {
                updateEtatCognitif(data.etat_cognitif);
            }
            
            // Flux de conscience
            if (data.pensees_actuelles && data.pensees_actuelles.flux_conscience) {
                updateFluxConscience(data.pensees_actuelles.flux_conscience);
            }
            
            // Sections pensées
            updateSectionsPensees(data.pensees_actuelles);
            
            // Processus actifs
            if (data.processus_actifs) {
                updateProcessusActifs(data.processus_actifs);
            }
            
            // Statistiques
            if (data.statistiques) {
                updateStatistiques(data.statistiques);
            }
        }

        // Mettre à jour l'état cognitif
        function updateEtatCognitif(etat) {
            document.getElementById('niveau-conscience').textContent = etat.niveau_conscience.toFixed(2);
            document.getElementById('profondeur-reflexion').textContent = etat.profondeur_reflexion.toFixed(2);
            document.getElementById('mode-pensee').textContent = etat.mode_pensee;
            document.getElementById('energie-mentale').textContent = etat.energie_mentale.toFixed(2);
            document.getElementById('confiance').textContent = etat.confiance.toFixed(2);
            document.getElementById('humeur').textContent = etat.humeur;
        }

        // Mettre à jour le flux de conscience
        function updateFluxConscience(pensees) {
            const container = document.getElementById('flux-container');
            
            pensees.forEach(pensee => {
                if (!document.getElementById(`pensee-${pensee.id}`)) {
                    const item = creerElementPensee(pensee, 'flux');
                    container.insertBefore(item, container.firstChild);
                }
            });
            
            // Limiter à 50 pensées
            while (container.children.length > 50) {
                container.removeChild(container.lastChild);
            }
        }

        // Mettre à jour les sections de pensées
        function updateSectionsPensees(pensees) {
            const sections = {
                'analytiques': 'pensees-analytiques',
                'creatives': 'pensees-creatives',
                'metacognition': 'pensees-metacognition',
                'emotions': 'pensees-emotions',
                'observations': 'pensees-observations',
                'decisions': 'pensees-decisions'
            };
            
            Object.entries(sections).forEach(([type, containerId]) => {
                if (pensees[type]) {
                    const container = document.getElementById(containerId);
                    container.innerHTML = '';
                    
                    pensees[type].forEach(pensee => {
                        const item = creerElementPensee(pensee, type.slice(0, -1)); // Enlever le 's'
                        container.appendChild(item);
                    });
                }
            });
        }

        // Créer un élément pensée COMPLET avec tous les détails
        function creerElementPensee(pensee, type) {
            const item = document.createElement('div');
            item.className = `pensee-item ${type}`;
            item.id = `pensee-${pensee.id}`;

            const temps = new Date(pensee.timestamp).toLocaleTimeString();
            const date = new Date(pensee.timestamp).toLocaleDateString();

            // Créer les détails complets selon le type de pensée
            let detailsHTML = creerDetailsComplets(pensee, type);

            item.innerHTML = `
                <div class="pensee-contenu">${pensee.contenu}</div>
                <div class="pensee-meta">
                    <span class="pensee-type">${pensee.type}</span>
                    <button class="pensee-expand-btn" onclick="toggleDetails('${pensee.id}')">
                        📋 Détails Complets
                    </button>
                    <span>${date} ${temps}</span>
                </div>
                <div class="pensee-details" id="details-${pensee.id}">
                    ${detailsHTML}
                </div>
            `;

            return item;
        }

        // Créer les détails complets pour chaque type de pensée
        function creerDetailsComplets(pensee, type) {
            let details = `
                <div class="pensee-detail-item">
                    <span class="pensee-detail-label">ID Unique:</span>
                    <span class="pensee-detail-value">${pensee.id}</span>
                </div>
                <div class="pensee-detail-item">
                    <span class="pensee-detail-label">Timestamp Complet:</span>
                    <span class="pensee-detail-value">${pensee.timestamp}</span>
                </div>
                <div class="pensee-detail-item">
                    <span class="pensee-detail-label">Type de Pensée:</span>
                    <span class="pensee-detail-value">${pensee.type}</span>
                </div>
            `;

            // Ajouter détails spécifiques selon le type
            switch(type) {
                case 'flux':
                    details += `
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Intensité:</span>
                            <span class="pensee-detail-value">${(pensee.intensite || 0).toFixed(3)}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Durée (ms):</span>
                            <span class="pensee-detail-value">${pensee.duree || 'N/A'}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Zone Cerveau:</span>
                            <span class="pensee-detail-value">${pensee.zone_cerveau || 'N/A'}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Niveau Conscience:</span>
                            <span class="pensee-detail-value">${(pensee.niveau_conscience || 0).toFixed(3)}</span>
                        </div>
                    `;
                    break;

                case 'analytique':
                    details += `
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Profondeur:</span>
                            <span class="pensee-detail-value">${(pensee.profondeur || 0).toFixed(3)}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Précision:</span>
                            <span class="pensee-detail-value">${(pensee.precision || 0).toFixed(3)}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Zone Cerveau:</span>
                            <span class="pensee-detail-value">${pensee.zone_cerveau || 'N/A'}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Processus Actif:</span>
                            <span class="pensee-detail-value">${pensee.processus_actif || 'N/A'}</span>
                        </div>
                    `;
                    break;

                case 'creative':
                    details += `
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Originalité:</span>
                            <span class="pensee-detail-value">${(pensee.originalite || 0).toFixed(3)}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Divergence:</span>
                            <span class="pensee-detail-value">${(pensee.divergence || 0).toFixed(3)}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Zone Cerveau:</span>
                            <span class="pensee-detail-value">${pensee.zone_cerveau || 'N/A'}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Processus Actif:</span>
                            <span class="pensee-detail-value">${pensee.processus_actif || 'N/A'}</span>
                        </div>
                    `;
                    break;

                case 'metacognition':
                    details += `
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Niveau Conscience:</span>
                            <span class="pensee-detail-value">${(pensee.niveau_conscience || 0).toFixed(3)}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Profondeur Réflexion:</span>
                            <span class="pensee-detail-value">${(pensee.profondeur_reflexion || 0).toFixed(3)}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Auto-évaluation:</span>
                            <span class="pensee-detail-value">${(pensee.auto_evaluation || 0).toFixed(3)}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Zone Cerveau:</span>
                            <span class="pensee-detail-value">${pensee.zone_cerveau || 'N/A'}</span>
                        </div>
                    `;
                    break;

                case 'emotion':
                    details += `
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Émotion:</span>
                            <span class="pensee-detail-value">${pensee.emotion || 'N/A'}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Intensité:</span>
                            <span class="pensee-detail-value">${(pensee.intensite || 0).toFixed(3)}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Valence:</span>
                            <span class="pensee-detail-value">${pensee.valence || 'N/A'}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Durée (ms):</span>
                            <span class="pensee-detail-value">${pensee.duree || 'N/A'}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Déclencheur:</span>
                            <span class="pensee-detail-value">${pensee.declencheur || 'N/A'}</span>
                        </div>
                    `;
                    break;

                case 'observation':
                    details += `
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Acuité:</span>
                            <span class="pensee-detail-value">${(pensee.acuite || 0).toFixed(3)}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Pertinence:</span>
                            <span class="pensee-detail-value">${(pensee.pertinence || 0).toFixed(3)}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Zone Cerveau:</span>
                            <span class="pensee-detail-value">${pensee.zone_cerveau || 'N/A'}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Processus Actif:</span>
                            <span class="pensee-detail-value">${pensee.processus_actif || 'N/A'}</span>
                        </div>
                    `;
                    break;

                case 'decision':
                    details += `
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Confiance:</span>
                            <span class="pensee-detail-value">${(pensee.confiance || 0).toFixed(3)}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Impact Estimé:</span>
                            <span class="pensee-detail-value">${(pensee.impact_estime || 0).toFixed(3)}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Réversibilité:</span>
                            <span class="pensee-detail-value">${pensee.reversibilite ? 'Oui' : 'Non'}</span>
                        </div>
                        <div class="pensee-detail-item">
                            <span class="pensee-detail-label">Zone Cerveau:</span>
                            <span class="pensee-detail-value">${pensee.zone_cerveau || 'N/A'}</span>
                        </div>
                    `;
                    break;
            }

            return details;
        }

        // Fonction pour afficher/masquer les détails
        function toggleDetails(penseeId) {
            const details = document.getElementById(`details-${penseeId}`);
            const penseeItem = document.getElementById(`pensee-${penseeId}`);

            if (details.classList.contains('visible')) {
                details.classList.remove('visible');
                penseeItem.classList.remove('expanded');
            } else {
                details.classList.add('visible');
                penseeItem.classList.add('expanded');
            }
        }

        // Mettre à jour les processus actifs
        function updateProcessusActifs(processus) {
            const container = document.getElementById('processus-grid');
            container.innerHTML = '';
            
            Object.entries(processus).forEach(([nom, actif]) => {
                const item = document.createElement('div');
                item.className = `processus-item ${actif ? 'actif' : 'inactif'}`;
                item.textContent = nom.replace(/_/g, ' ');
                container.appendChild(item);
            });
        }

        // Mettre à jour les statistiques
        function updateStatistiques(stats) {
            document.getElementById('total-pensees').textContent = stats.total_pensees;
            document.getElementById('pensees-minute').textContent = stats.pensees_par_minute;
            document.getElementById('processus-actifs-count').textContent = stats.processus_actifs_count;
            document.getElementById('activite-mentale').textContent = (stats.niveau_activite_mentale * 100).toFixed(1) + '%';
        }

        // Contrôles
        function toggleLecture() {
            lectureActive = !lectureActive;
            const btn = document.getElementById('btn-lecture');
            
            if (lectureActive) {
                btn.textContent = '🔄 Lecture Active';
                btn.classList.add('actif');
                demarrerLecturePensees();
            } else {
                btn.textContent = '⏸️ Lecture Pausée';
                btn.classList.remove('actif');
                if (updateInterval) clearInterval(updateInterval);
            }
        }

        function viderPensees() {
            document.getElementById('flux-container').innerHTML = '';
            document.querySelectorAll('.section-container').forEach(container => {
                container.innerHTML = '';
            });
        }

        function sauvegarderPensees() {
            // TODO: Implémenter sauvegarde
            alert('Pensées sauvegardées !');
        }

        function exporterPensees() {
            // TODO: Implémenter export
            alert('Export des pensées en cours...');
        }
    </script>
</body>
</html>
