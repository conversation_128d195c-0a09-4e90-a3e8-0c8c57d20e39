// 🧠 LOUNA AI - SERVEUR SIMPLE FONCTIONNEL
const express = require('express');
const http = require('http');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = 3001;

console.log('🚀 Démarrage LOUNA AI Simple...');

// Middleware de base
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Servir les fichiers statiques
app.use(express.static('public'));
app.use('/css', express.static('public/css'));
app.use('/js', express.static('public/js'));

// Route principale - Interface LOUNA-AI
app.get('/', (req, res) => {
    // Vérifier si l'interface complète existe
    const interfaceCompleteFile = path.join(__dirname, 'louna-interface-complete.html');
    if (fs.existsSync(interfaceCompleteFile)) {
        res.sendFile(interfaceCompleteFile);
    } else {
        // Fallback vers l'interface existante
        const indexFile = path.join(__dirname, 'index.html');
        if (fs.existsSync(indexFile)) {
            res.sendFile(indexFile);
        } else {
            res.send(`
                <html>
                <head>
                    <title>LOUNA-AI</title>
                    <style>
                        body { font-family: Arial, sans-serif; background: #1a1a2e; color: white; padding: 50px; text-align: center; }
                        h1 { color: #ff6b9d; font-size: 3em; }
                        .status { background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin: 20px 0; }
                    </style>
                </head>
                <body>
                    <h1>🧠 LOUNA-AI</h1>
                    <div class="status">
                        <h2>✅ Serveur Actif</h2>
                        <p>QI: 235 - Génie Exceptionnel</p>
                        <p>Port: ${PORT}</p>
                        <p>Statut: Opérationnel</p>
                    </div>
                    <p>Interface en cours de chargement...</p>
                </body>
                </html>
            `);
        }
    }
});

// Routes des applications existantes
const routes = [
    { path: '/chat', file: 'public/chat-cognitif-complet.html', name: 'Chat Cognitif' },
    { path: '/thermal-memory', file: 'public/futuristic-interface.html', name: 'Mémoire Thermique' },
    { path: '/brain-monitoring', file: 'public/brain-monitoring-complete.html', name: 'Monitoring Cerveau' },
    { path: '/brain-3d', file: 'public/brain-visualization.html', name: 'Visualisation 3D' },
    { path: '/generation', file: 'public/generation-center.html', name: 'Centre de Génération' },
    { path: '/images', file: 'public/image-generator.html', name: 'Génération Images' },
    { path: '/video', file: 'public/video-generator.html', name: 'Génération Vidéo' },
    { path: '/music', file: 'music-generator.html', name: 'Générateur Musical' },
    { path: '/accelerators', file: 'accelerators-dashboard.html', name: 'Accélérateurs Kyber' },
    { path: '/code-editor', file: 'public/code-editor.html', name: 'Éditeur Code' },
    { path: '/emergency', file: 'public/emergency-control.html', name: 'Contrôle Urgence' }
];

// Créer les routes automatiquement
routes.forEach(route => {
    app.get(route.path, (req, res) => {
        const filePath = path.join(__dirname, route.file);
        if (fs.existsSync(filePath)) {
            res.sendFile(filePath);
        } else {
            res.send(`
                <html>
                <head>
                    <title>${route.name} - LOUNA AI</title>
                    <style>
                        body { font-family: Arial, sans-serif; background: #1a1a2e; color: white; padding: 50px; text-align: center; }
                        h1 { color: #ff6b9d; }
                        .back-btn { background: #ff6b9d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
                    </style>
                </head>
                <body>
                    <h1>🧠 ${route.name}</h1>
                    <p>Module en cours de chargement...</p>
                    <p>Fichier recherché: ${route.file}</p>
                    <a href="/" class="back-btn">← Retour à l'accueil</a>
                </body>
                </html>
            `);
        }
    });
});

// API pour le monitoring système
app.get('/api/monitoring/system', (req, res) => {
    const baseTime = Date.now();
    const variation = Math.sin(baseTime / 30000) * 0.05;
    
    const data = {
        qi: {
            current: Math.round(235 + variation * 10),
            level: "Génie Exceptionnel",
            trend: variation > 0 ? "croissant" : "stable"
        },
        neurons: {
            total: Math.round(148 + variation * 5),
            active: Math.round(92 + variation * 8),
            efficiency: Math.round((89.5 + variation * 3) * 10) / 10
        },
        memory: {
            temperature: Math.round((67.4 + variation * 3) * 10) / 10,
            zones: 6,
            accelerators: 24,
            status: "Actif"
        },
        ollama: {
            connected: false, // Sera mis à jour quand Ollama sera connecté
            model: "deepseek-r1:7b",
            status: "En attente"
        },
        system: {
            status: "Opérationnel",
            uptime: Math.floor(Date.now() / 1000),
            temperature: Math.round(45 + variation * 5)
        },
        timestamp: new Date().toISOString()
    };
    
    res.json(data);
});

// API de statut
app.get('/status', (req, res) => {
    res.json({
        server: 'running',
        version: '1.0.0-simple',
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
        qi: 235,
        status: 'Opérationnel',
        port: PORT
    });
});

// Démarrage du serveur
const server = app.listen(PORT, () => {
    console.log('');
    console.log('🎯 ========================================');
    console.log('🧠 LOUNA AI SIMPLE - INTERFACE ACTIVE');
    console.log('🎯 ========================================');
    console.log('');
    console.log(`🚀 Serveur démarré sur: http://localhost:${PORT}`);
    console.log(`📊 Interface principale: http://localhost:${PORT}`);
    console.log(`🧠 QI: 235 - Génie Exceptionnel`);
    console.log(`⚡ Système Neuronal Adaptatif`);
    console.log(`🔥 Prêt pour connexion Ollama`);
    console.log('');
    console.log('✅ LOUNA AI est maintenant prête !');
    console.log('');
    console.log('📋 Applications disponibles:');
    routes.forEach(route => {
        console.log(`   ${route.path} - ${route.name}`);
    });
    console.log('');
});

// Gestion propre de l'arrêt
process.on('SIGTERM', () => {
    console.log('🛑 Arrêt du serveur...');
    server.close(() => {
        console.log('✅ Serveur arrêté proprement');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('🛑 Arrêt du serveur...');
    server.close(() => {
        console.log('✅ Serveur arrêté proprement');
        process.exit(0);
    });
});

module.exports = app;
