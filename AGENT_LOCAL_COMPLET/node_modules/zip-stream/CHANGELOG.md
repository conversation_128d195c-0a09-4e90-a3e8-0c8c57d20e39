## Changelog

**4.1.1** - <small>September 2, 2023</small> — [Diff](https://github.com/archiverjs/node-zip-stream/compare/4.1.0...4.1.1)

**4.1.0** - <small>March 2, 2021</small> — [Diff](https://github.com/archiverjs/node-zip-stream/compare/4.0.5...4.1.0)

## Features

- Allow prepending forward slash in entry name (#79)

## Maintenance

- Bump mocha from 8.2.1 to 8.3.0 (#76)
- Bump actions/setup-node from v2.1.4 to v2.1.5 (#77)
- Bump actions/setup-node from v2.1.2 to v2.1.4 (#74)

**4.0.5** - <small>November 18, 2020</small> — [Diff](https://github.com/archiverjs/node-zip-stream/compare/4.0.4...4.0.5)

- No Changes

**4.0.4** - <small>November 18, 2020</small> — [Diff](https://github.com/archiverjs/node-zip-stream/compare/4.0.3...4.0.4)

### Maintenance

- Bump compress-commons from 4.0.1 to 4.0.2 (#72)

**4.0.3** - <small>November 18, 2020</small> — [Diff](https://github.com/archiverjs/node-zip-stream/compare/4.0.2...4.0.3)

### Maintenance

- Update docs example with latest ES6 syntax (#60)
- Update archiver-jsdoc-theme to ^1.1.3 (#71)
- Update archiver-jsdoc-theme to ^1.1.2 (#70)
- Bump jsdoc from 3.6.5 to 3.6.6 (#62)
- Bump actions/setup-node from v2.1.1 to v2.1.2 (#64)
- Bump mocha from 8.1.1 to 8.2.1 (#66)
- Bump actions/checkout from v2.3.2 to v2.3.4 (#67)
- Bump mocha from 8.1.0 to 8.1.1 (#55)
- Bump actions/checkout from v2.3.1 to v2.3.2 (#56)
- Bump mocha from 8.0.1 to 8.1.0 (#54)
- Bump actions/setup-node from v2.1.0 to v2.1.1 (#52)
- Bump jsdoc from 3.6.4 to 3.6.5 (#53)


**4.0.2** - <small>July 20, 2020</small> — [Diff](https://github.com/archiverjs/node-zip-stream/compare/4.0.1...4.0.2)

* Bump compress-commons from 4.0.0 to 4.0.1 (#51) @dependabot

**4.0.1** - <small>July 20, 2020</small> — [Diff](https://github.com/archiverjs/node-zip-stream/compare/4.0.0...4.0.1)

* Bump compress-commons from 3.0.0 to 4.0.0 (#50) @dependabot

**4.0.0** - <small>July 18, 2020</small> — [Diff](https://github.com/archiverjs/node-zip-stream/compare/3.0.1...4.0.0)

* Bump mocha from 6.2.3 to 8.0.1 (#47) @dependabot
* Bump rimraf from 2.7.1 to 3.0.2 (#46) @dependabot
* Bump actions/setup-node from v1 to v2.1.0 (#45) @dependabot
* Bump mkdirp from 0.5.5 to 1.0.4 (#48) @dependabot
* remove support for node < 10 (#49) @ctalkington
* Bump actions/checkout from v1 to v2.3.1 (#44) @dependabot

**3.0.1** - <small>April 14, 2020</small> — [Diff](https://github.com/archiverjs/node-zip-stream/compare/3.0.0...3.0.1)

- update to compress-commons@^3.0.0

**3.0.0** - <small>April 14, 2020</small> — [Diff](https://github.com/archiverjs/node-zip-stream/compare/2.1.3...3.0.0)

- breaking: slowly catch up with node LTS, remove support for versions under 8.
- update multiple deps.

**2.1.3** — <small> January 8, 2020 </small> — [Diff](https://github.com/archiverjs/node-zip-stream/compare/2.1.0...2.1.3)

- allow zip64 for no compression (#40)

**2.1.2** — <small> August 2, 2019 </small> — [Diff](https://github.com/archiverjs/node-zip-stream/compare/2.1.0...2.1.2)

- update compress-commons to v2.1.1

**2.1.1** — <small> August 2, 2019 </small> — [Diff](https://github.com/archiverjs/node-zip-stream/compare/2.1.0...2.1.1)

- update compress-commons to v2.1.0

**2.1.0** — <small> July 19, 2019 </small> — [Diff](https://github.com/archiverjs/node-zip-stream/compare/2.0.1...2.1.0)

- test: now targeting node v12
- other: update dependencies.

**2.0.1** — <small> August 22, 2018 </small> — [Diff](https://github.com/archiverjs/node-zip-stream/compare/2.0.0...2.0.1)

- update to archiver-utils@2

**2.0.0** — <small> August 22, 2018 </small> — [Diff](https://github.com/archiverjs/node-zip-stream/compare/1.2.0...2.0.0)

- breaking: follow node LTS, remove support for versions under 6.
- other: remove unused lodash dependence (#35)

**1.2.0** — <small> June 16, 2017 </small> — [Diff](https://github.com/archiverjs/node-zip-stream/compare/1.1.1...1.2.0)

- groundwork for symlinks support.

*NOTE: this will be the last release for node v0.10 and v0.12. node v4 will become the minimum and a version bump to 2.0.0 will take place.*

**1.1.1** — <small>_January 17, 2017_</small> — [Diff](https://github.com/archiverjs/node-zip-stream/compare/1.1.0...1.1.1)

- actually use STORE method if level is 0 (GH #21)
- bump deps to ensure latest versions are used.

**1.1.0** — <small>_August 27, 2016_</small> — [Diff](https://github.com/archiverjs/node-zip-stream/compare/1.0.0...1.1.0)

- bump deps to ensure latest versions are used.

[Release Archive](https://github.com/archiverjs/node-zip-stream/releases)
