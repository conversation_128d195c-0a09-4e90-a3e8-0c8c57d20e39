#!/bin/bash

# Vérifier si Ollama est installé
if ! command -v ollama &> /dev/null; then
    echo "Ollama n'est pas installé. Veuillez l'installer depuis https://ollama.com/download"
    exit 1
fi

# Vérifier si Ollama est en cours d'exécution
if ! curl -s http://localhost:11434/api/version &> /dev/null; then
    echo "Démarrage d'Ollama..."
    ollama serve &
    sleep 2
fi

# Copier le fichier index-simple.ejs vers index.ejs
cp views/index-simple.ejs views/index.ejs

# Démarrer l'application simplifiée
echo "Démarrage de l'interface DeepSeek r1 Simplifiée..."
node server-simple.js
