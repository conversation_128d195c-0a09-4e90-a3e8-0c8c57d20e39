<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>CFBundleIdentifier</key>
    <string>com.anthropic.claudefordesktop.helper</string>
    <key>CFBundleName</key>
    <string><PERSON> (Plugin)</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>DTCompiler</key>
    <string>com.apple.compilers.llvm.clang.1_0</string>
    <key>DTSDKBuild</key>
    <string>23F73</string>
    <key>DTSDKName</key>
    <string>macosx14.5</string>
    <key>DTXcode</key>
    <string>1540</string>
    <key>DTXcodeBuild</key>
    <string>15F31d</string>
    <key>LSEnvironment</key>
    <dict>
      <key>MallocNanoZone</key>
      <string>0</string>
    </dict>
    <key>LSUIElement</key>
    <true/>
    <key>NSSupportsAutomaticGraphicsSwitching</key>
    <true/>
    <key>CFBundleDisplayName</key>
    <string>Claude Helper (Plugin)</string>
    <key>CFBundleExecutable</key>
    <string>Claude Helper (Plugin)</string>
    <key>CFBundleVersion</key>
    <string>0.9.3</string>
    <key>CFBundleShortVersionString</key>
    <string>0.9.3</string>
    <key>ElectronAsarIntegrity</key>
    <dict>
      <key>Resources/app.asar</key>
      <dict>
        <key>algorithm</key>
        <string>SHA256</string>
        <key>hash</key>
        <string>9635b1d43f379c91c6ea23d7c0d3ea2f68841dc248952da1ed8d9d005dedd4c2</string>
      </dict>
    </dict>
  </dict>
</plist>