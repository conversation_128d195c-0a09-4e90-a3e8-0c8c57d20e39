#!/bin/bash

echo "🚀 ========================================"
echo "🧠 LANCEMENT LOUNA-AI SYSTÈME COMPLET"
echo "🚀 ========================================"
echo ""

# Vérifier que nous sommes dans le bon répertoire
if [ ! -f "server.js" ]; then
    echo "❌ Erreur: server.js non trouvé"
    echo "📍 Assurez-vous d'être dans le répertoire LOUNA-AI-INTERFACE-COMPLETE"
    exit 1
fi

echo "✅ Répertoire correct détecté"
echo "📍 $(pwd)"
echo ""

# Créer le dossier MEMOIRE-REELLE si nécessaire
if [ ! -d "MEMOIRE-REELLE" ]; then
    echo "📁 Création dossier MEMOIRE-REELLE..."
    mkdir -p MEMOIRE-REELLE
fi

# Démarrer tous les modules en arrière-plan
echo "🧠 Démarrage système évolution QI..."
node evolution-qi-automatique.js &
EVOLUTION_PID=$!
echo "✅ Évolution QI démarrée (PID: $EVOLUTION_PID)"

sleep 1

echo "⚡ Démarrage optimiseur performance..."
node optimiseur-performance-reel.js &
OPTIMISEUR_PID=$!
echo "✅ Optimiseur performance démarré (PID: $OPTIMISEUR_PID)"

sleep 1

echo "🚀 Démarrage accélérateur réponses..."
node accelerateur-reponses.js &
ACCELERATEUR_PID=$!
echo "✅ Accélérateur réponses démarré (PID: $ACCELERATEUR_PID)"

sleep 1

echo "📊 Démarrage moniteur système..."
node moniteur-systeme-reel.js &
MONITEUR_PID=$!
echo "✅ Moniteur système démarré (PID: $MONITEUR_PID)"

sleep 2

echo "🌐 Démarrage serveur principal..."
node server.js &
SERVER_PID=$!
echo "✅ Serveur principal démarré (PID: $SERVER_PID)"

echo ""
echo "🎯 SYSTÈME COMPLET DÉMARRÉ !"
echo "================================"
echo "🌐 Interface principale: http://localhost:3001"
echo ""
echo "📊 APIs disponibles:"
echo "• QI Evolution: http://localhost:3001/api/qi-evolution"
echo "• Performance: http://localhost:3001/api/performance"
echo "• Accélération: http://localhost:3001/api/acceleration"
echo "• Monitoring: http://localhost:3001/api/monitoring"
echo ""
echo "🔧 Modules actifs:"
echo "• 🧠 Évolution QI automatique (toutes les 30s)"
echo "• ⚡ Optimiseur performance (toutes les 15s)"
echo "• 🚀 Accélérateur réponses (cache intelligent)"
echo "• 📊 Moniteur système (toutes les 10s)"
echo "• 🌐 Interface web complète"
echo ""
echo "🛑 Pour arrêter: Appuyez sur Ctrl+C"
echo ""

# Fonction pour arrêter proprement tous les processus
cleanup() {
    echo ""
    echo "🛑 Arrêt du système LOUNA-AI complet..."
    echo "🔄 Arrêt évolution QI (PID: $EVOLUTION_PID)..."
    kill $EVOLUTION_PID 2>/dev/null
    echo "🔄 Arrêt optimiseur (PID: $OPTIMISEUR_PID)..."
    kill $OPTIMISEUR_PID 2>/dev/null
    echo "🔄 Arrêt accélérateur (PID: $ACCELERATEUR_PID)..."
    kill $ACCELERATEUR_PID 2>/dev/null
    echo "🔄 Arrêt moniteur (PID: $MONITEUR_PID)..."
    kill $MONITEUR_PID 2>/dev/null
    echo "🔄 Arrêt serveur principal (PID: $SERVER_PID)..."
    kill $SERVER_PID 2>/dev/null
    
    sleep 2
    
    # Forcer l'arrêt si nécessaire
    echo "🧹 Nettoyage final..."
    pkill -f "evolution-qi-automatique.js" 2>/dev/null
    pkill -f "optimiseur-performance-reel.js" 2>/dev/null
    pkill -f "accelerateur-reponses.js" 2>/dev/null
    pkill -f "moniteur-systeme-reel.js" 2>/dev/null
    
    echo "✅ Système arrêté proprement"
    exit 0
}

# Capturer Ctrl+C et autres signaux
trap cleanup SIGINT SIGTERM

# Afficher le statut toutes les 30 secondes
while true; do
    sleep 30
    echo "💓 Système LOUNA-AI actif - $(date '+%H:%M:%S')"
    
    # Vérifier que tous les processus sont encore actifs
    if ! kill -0 $SERVER_PID 2>/dev/null; then
        echo "❌ Serveur principal arrêté - Redémarrage..."
        node server.js &
        SERVER_PID=$!
    fi
    
    if ! kill -0 $EVOLUTION_PID 2>/dev/null; then
        echo "❌ Évolution QI arrêtée - Redémarrage..."
        node evolution-qi-automatique.js &
        EVOLUTION_PID=$!
    fi
    
    if ! kill -0 $OPTIMISEUR_PID 2>/dev/null; then
        echo "❌ Optimiseur arrêté - Redémarrage..."
        node optimiseur-performance-reel.js &
        OPTIMISEUR_PID=$!
    fi
    
    if ! kill -0 $ACCELERATEUR_PID 2>/dev/null; then
        echo "❌ Accélérateur arrêté - Redémarrage..."
        node accelerateur-reponses.js &
        ACCELERATEUR_PID=$!
    fi
    
    if ! kill -0 $MONITEUR_PID 2>/dev/null; then
        echo "❌ Moniteur arrêté - Redémarrage..."
        node moniteur-systeme-reel.js &
        MONITEUR_PID=$!
    fi
done
