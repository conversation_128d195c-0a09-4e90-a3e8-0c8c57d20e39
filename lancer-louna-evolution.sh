#!/bin/bash

echo "🚀 ========================================"
echo "🧠 LANCEMENT LOUNA-AI AVEC ÉVOLUTION QI"
echo "🚀 ========================================"
echo ""

# Vérifier que nous sommes dans le bon répertoire
if [ ! -f "server.js" ]; then
    echo "❌ Erreur: server.js non trouvé"
    echo "📍 Assurez-vous d'être dans le répertoire LOUNA-AI-INTERFACE-COMPLETE"
    exit 1
fi

echo "✅ Répertoire correct détecté"
echo "📍 $(pwd)"
echo ""

# Démarrer le système d'évolution QI en arrière-plan
echo "🧠 Démarrage système évolution QI..."
node evolution-qi-automatique.js &
EVOLUTION_PID=$!
echo "✅ Évolution QI démarrée (PID: $EVOLUTION_PID)"

# Attendre un peu pour que l'évolution s'initialise
sleep 2

# Démarrer le serveur principal
echo "🚀 Démarrage serveur principal..."
node server.js &
SERVER_PID=$!
echo "✅ Serveur principal démarré (PID: $SERVER_PID)"

echo ""
echo "🎯 SYSTÈME COMPLET DÉMARRÉ !"
echo "================================"
echo "🌐 Interface: http://localhost:3001"
echo "📊 API QI Evolution: http://localhost:3001/api/qi-evolution"
echo "🧠 QI évoluera automatiquement toutes les 30 secondes"
echo ""
echo "🛑 Pour arrêter: Appuyez sur Ctrl+C"
echo ""

# Fonction pour arrêter proprement
cleanup() {
    echo ""
    echo "🛑 Arrêt du système LOUNA-AI..."
    echo "🔄 Arrêt évolution QI (PID: $EVOLUTION_PID)..."
    kill $EVOLUTION_PID 2>/dev/null
    echo "🔄 Arrêt serveur principal (PID: $SERVER_PID)..."
    kill $SERVER_PID 2>/dev/null
    echo "✅ Système arrêté proprement"
    exit 0
}

# Capturer Ctrl+C
trap cleanup SIGINT SIGTERM

# Attendre que les processus se terminent
wait $SERVER_PID $EVOLUTION_PID
