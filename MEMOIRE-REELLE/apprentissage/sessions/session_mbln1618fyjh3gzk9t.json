{"id": "mbln1618fyjh3gzk9t", "type": "resolution_problemes", "debut": "2025-06-07T02:51:31.148Z", "duree_prevue": 180000, "qi_debut": 157, "objectifs": ["Résoudre problèmes complexes", "Optimiser méthodes", "Innover solutions"], "progres": [{"probleme": "Problème de complexité 1", "complexite": 1, "solution": "approche_systematique", "temps": 1073.9530102542035}, {"probleme": "Problème de complexité 2", "complexite": 2, "solution": "approche_systematique", "temps": 229.80409082179102}, {"probleme": "Problème de complexité 3", "complexite": 3, "solution": "approche_systematique", "temps": 318.73447977623516}, {"probleme": "Problème de complexité 4", "complexite": 4, "solution": "approche_systematique", "temps": 312.62280588350535}, {"probleme": "Problème de complexité 5", "complexite": 5, "solution": "approche_systematique", "temps": 940.7339079367713}], "fin": "2025-06-07T02:51:32.356Z", "duree_reelle": 1208, "qi_fin": 157, "gain_qi": 0}