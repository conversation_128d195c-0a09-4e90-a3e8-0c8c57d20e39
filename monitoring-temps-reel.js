#!/usr/bin/env node

/**
 * SYSTÈME DE MONITORING TEMPS RÉEL AVANCÉ
 * Surveillance complète de tous les composants LOUNA-AI
 */

const os = require('os');
const fs = require('fs');
const path = require('path');

class MonitoringTempsReel {
    constructor() {
        console.log('📊 ========================================');
        console.log('🔍 MONITORING TEMPS RÉEL AVANCÉ LOUNA-AI');
        console.log('📊 ========================================');
        
        this.metriques = {
            systeme: {
                cpu: { utilisation: 0, temperature: 0, frequence: 0 },
                memoire: { utilisee: 0, disponible: 0, pourcentage: 0 },
                disque: { utilise: 0, disponible: 0, pourcentage: 0 },
                reseau: { entrant: 0, sortant: 0, latence: 0 }
            },
            agent: {
                qi_actuel: 126,
                performance: 0,
                connexions_actives: 0,
                sessions_apprentissage: 0,
                erreurs: 0
            },
            ollama: {
                actif: false,
                modele_charge: null,
                temps_reponse_moyen: 0,
                requetes_traitees: 0,
                erreurs: 0
            },
            memoire_thermique: {
                zones_actives: 0,
                temperature_moyenne: 0,
                flux_neural: 0,
                synapses_actives: 0
            },
            applications: {
                detectees: 0,
                integrables: 0,
                actives: 0
            }
        };
        
        this.alertes = [];
        this.historique = [];
        this.seuils = {
            cpu_critique: 90,
            memoire_critique: 95,
            temperature_critique: 80,
            temps_reponse_critique: 5000,
            erreurs_max: 10
        };
        
        this.dossierMonitoring = './MEMOIRE-REELLE/monitoring';
        this.initialiserDossiers();
        this.demarrerMonitoring();
        
        console.log('📊 Monitoring temps réel initialisé');
    }

    // INITIALISER DOSSIERS
    initialiserDossiers() {
        if (!fs.existsSync('./MEMOIRE-REELLE')) {
            fs.mkdirSync('./MEMOIRE-REELLE', { recursive: true });
        }
        
        if (!fs.existsSync(this.dossierMonitoring)) {
            fs.mkdirSync(this.dossierMonitoring, { recursive: true });
        }
        
        // Créer sous-dossiers
        const sousDossiers = ['logs', 'alertes', 'historique', 'rapports'];
        sousDossiers.forEach(dossier => {
            const chemin = path.join(this.dossierMonitoring, dossier);
            if (!fs.existsSync(chemin)) {
                fs.mkdirSync(chemin, { recursive: true });
            }
        });
    }

    // DÉMARRER MONITORING
    demarrerMonitoring() {
        console.log('🔄 Démarrage monitoring continu...');
        
        // Monitoring système toutes les 5 secondes
        setInterval(() => {
            this.surveillerSysteme();
        }, 5000);
        
        // Monitoring agent toutes les 10 secondes
        setInterval(() => {
            this.surveillerAgent();
        }, 10000);
        
        // Monitoring Ollama toutes les 15 secondes
        setInterval(() => {
            this.surveillerOllama();
        }, 15000);
        
        // Sauvegarde historique toutes les minutes
        setInterval(() => {
            this.sauvegarderHistorique();
        }, 60000);
        
        // Génération rapport toutes les heures
        setInterval(() => {
            this.genererRapport();
        }, 3600000);
    }

    // SURVEILLER SYSTÈME
    surveillerSysteme() {
        try {
            // CPU
            const cpus = os.cpus();
            const loadAvg = os.loadavg();
            this.metriques.systeme.cpu.utilisation = Math.min((loadAvg[0] / cpus.length) * 100, 100);
            this.metriques.systeme.cpu.frequence = cpus[0].speed;
            this.metriques.systeme.cpu.temperature = this.obtenirTemperatureCPU();
            
            // Mémoire
            const totalMem = os.totalmem();
            const freeMem = os.freemem();
            const usedMem = totalMem - freeMem;
            this.metriques.systeme.memoire.utilisee = usedMem;
            this.metriques.systeme.memoire.disponible = freeMem;
            this.metriques.systeme.memoire.pourcentage = (usedMem / totalMem) * 100;
            
            // Disque
            this.surveillerDisque();
            
            // Réseau
            this.surveillerReseau();
            
            // Vérifier seuils critiques
            this.verifierSeuilsCritiques();
            
        } catch (error) {
            this.ajouterAlerte('erreur', 'Erreur monitoring système: ' + error.message);
        }
    }

    // SURVEILLER AGENT
    async surveillerAgent() {
        try {
            // Tenter de récupérer le statut de l'agent
            const response = await fetch('http://localhost:3001/api/agent/status').catch(() => null);
            
            if (response && response.ok) {
                const data = await response.json();
                
                if (data.success && data.agent) {
                    this.metriques.agent.qi_actuel = data.agent.agent.qi_actuel;
                    this.metriques.agent.performance = data.agent.agent.performance.efficacite;
                    
                    const connexions = Object.values(data.agent.agent.connexions).filter(Boolean).length;
                    this.metriques.agent.connexions_actives = connexions;
                    
                    this.metriques.agent.sessions_apprentissage = data.agent.agent.sessions_apprentissage || 0;
                }
            } else {
                this.metriques.agent.erreurs++;
                this.ajouterAlerte('warning', 'Agent non accessible');
            }
            
        } catch (error) {
            this.metriques.agent.erreurs++;
            this.ajouterAlerte('erreur', 'Erreur monitoring agent: ' + error.message);
        }
    }

    // SURVEILLER OLLAMA
    async surveillerOllama() {
        try {
            const response = await fetch('http://localhost:3001/api/test-ollama').catch(() => null);
            
            if (response && response.ok) {
                const data = await response.json();
                
                if (data.success && data.test) {
                    this.metriques.ollama.actif = true;
                    this.metriques.ollama.modele_charge = 'llama3.2:1b';
                    this.metriques.ollama.temps_reponse_moyen = data.test.duree || 0;
                    this.metriques.ollama.requetes_traitees++;
                } else {
                    this.metriques.ollama.actif = false;
                    this.metriques.ollama.erreurs++;
                }
            } else {
                this.metriques.ollama.actif = false;
                this.metriques.ollama.erreurs++;
                this.ajouterAlerte('warning', 'Ollama non accessible');
            }
            
        } catch (error) {
            this.metriques.ollama.actif = false;
            this.metriques.ollama.erreurs++;
            this.ajouterAlerte('erreur', 'Erreur monitoring Ollama: ' + error.message);
        }
    }

    // SURVEILLER DISQUE
    surveillerDisque() {
        try {
            // Simuler surveillance disque (en production: vraie surveillance)
            this.metriques.systeme.disque.utilise = Math.random() * 500 + 100; // GB
            this.metriques.systeme.disque.disponible = Math.random() * 200 + 50; // GB
            this.metriques.systeme.disque.pourcentage = 
                (this.metriques.systeme.disque.utilise / 
                (this.metriques.systeme.disque.utilise + this.metriques.systeme.disque.disponible)) * 100;
                
        } catch (error) {
            this.ajouterAlerte('erreur', 'Erreur surveillance disque: ' + error.message);
        }
    }

    // SURVEILLER RÉSEAU
    surveillerReseau() {
        try {
            // Simuler surveillance réseau
            this.metriques.systeme.reseau.entrant = Math.random() * 100; // MB/s
            this.metriques.systeme.reseau.sortant = Math.random() * 50; // MB/s
            this.metriques.systeme.reseau.latence = Math.random() * 50 + 10; // ms
            
        } catch (error) {
            this.ajouterAlerte('erreur', 'Erreur surveillance réseau: ' + error.message);
        }
    }

    // OBTENIR TEMPÉRATURE CPU
    obtenirTemperatureCPU() {
        try {
            // Simuler température CPU (en production: vraie lecture)
            return Math.random() * 20 + 35; // 35-55°C
        } catch (error) {
            return 0;
        }
    }

    // VÉRIFIER SEUILS CRITIQUES
    verifierSeuilsCritiques() {
        const maintenant = new Date();
        
        // CPU critique
        if (this.metriques.systeme.cpu.utilisation > this.seuils.cpu_critique) {
            this.ajouterAlerte('critique', 
                `CPU critique: ${this.metriques.systeme.cpu.utilisation.toFixed(1)}%`);
        }
        
        // Mémoire critique
        if (this.metriques.systeme.memoire.pourcentage > this.seuils.memoire_critique) {
            this.ajouterAlerte('critique', 
                `Mémoire critique: ${this.metriques.systeme.memoire.pourcentage.toFixed(1)}%`);
        }
        
        // Température critique
        if (this.metriques.systeme.cpu.temperature > this.seuils.temperature_critique) {
            this.ajouterAlerte('critique', 
                `Température critique: ${this.metriques.systeme.cpu.temperature.toFixed(1)}°C`);
        }
        
        // Temps de réponse Ollama critique
        if (this.metriques.ollama.temps_reponse_moyen > this.seuils.temps_reponse_critique) {
            this.ajouterAlerte('warning', 
                `Ollama lent: ${this.metriques.ollama.temps_reponse_moyen}ms`);
        }
        
        // Trop d'erreurs
        if (this.metriques.agent.erreurs > this.seuils.erreurs_max) {
            this.ajouterAlerte('critique', 
                `Trop d'erreurs agent: ${this.metriques.agent.erreurs}`);
        }
    }

    // AJOUTER ALERTE
    ajouterAlerte(niveau, message) {
        const alerte = {
            id: this.genererIdUnique(),
            niveau: niveau, // 'info', 'warning', 'erreur', 'critique'
            message: message,
            timestamp: new Date().toISOString(),
            resolu: false
        };
        
        this.alertes.unshift(alerte);
        
        // Limiter à 100 alertes
        if (this.alertes.length > 100) {
            this.alertes = this.alertes.slice(0, 100);
        }
        
        // Sauvegarder alerte critique
        if (niveau === 'critique') {
            this.sauvegarderAlerte(alerte);
        }
        
        console.log(`🚨 [${niveau.toUpperCase()}] ${message}`);
    }

    // SAUVEGARDER HISTORIQUE
    sauvegarderHistorique() {
        try {
            const snapshot = {
                timestamp: new Date().toISOString(),
                metriques: JSON.parse(JSON.stringify(this.metriques)),
                alertes_actives: this.alertes.filter(a => !a.resolu).length
            };
            
            this.historique.push(snapshot);
            
            // Limiter historique à 1440 entrées (24h à 1 minute d'intervalle)
            if (this.historique.length > 1440) {
                this.historique = this.historique.slice(-1440);
            }
            
            // Sauvegarder sur disque toutes les 10 minutes
            if (this.historique.length % 10 === 0) {
                const fichier = path.join(this.dossierMonitoring, 'historique', 
                    `historique_${new Date().toISOString().slice(0, 10)}.json`);
                fs.writeFileSync(fichier, JSON.stringify(this.historique, null, 2));
            }
            
        } catch (error) {
            console.log('❌ Erreur sauvegarde historique:', error.message);
        }
    }

    // SAUVEGARDER ALERTE
    sauvegarderAlerte(alerte) {
        try {
            const fichier = path.join(this.dossierMonitoring, 'alertes', 
                `alerte_${alerte.id}.json`);
            fs.writeFileSync(fichier, JSON.stringify(alerte, null, 2));
        } catch (error) {
            console.log('❌ Erreur sauvegarde alerte:', error.message);
        }
    }

    // GÉNÉRER RAPPORT
    genererRapport() {
        try {
            const maintenant = new Date();
            const rapport = {
                timestamp: maintenant.toISOString(),
                periode: '1 heure',
                resume: {
                    cpu_moyen: this.calculerMoyenne('systeme.cpu.utilisation'),
                    memoire_moyenne: this.calculerMoyenne('systeme.memoire.pourcentage'),
                    temperature_moyenne: this.calculerMoyenne('systeme.cpu.temperature'),
                    qi_actuel: this.metriques.agent.qi_actuel,
                    ollama_disponibilite: this.calculerDisponibilite('ollama'),
                    alertes_total: this.alertes.length,
                    alertes_critiques: this.alertes.filter(a => a.niveau === 'critique').length
                },
                recommandations: this.genererRecommandations()
            };
            
            const fichier = path.join(this.dossierMonitoring, 'rapports', 
                `rapport_${maintenant.toISOString().slice(0, 13)}.json`);
            fs.writeFileSync(fichier, JSON.stringify(rapport, null, 2));
            
            console.log('📋 Rapport horaire généré');
            
        } catch (error) {
            console.log('❌ Erreur génération rapport:', error.message);
        }
    }

    // CALCULER MOYENNE
    calculerMoyenne(chemin) {
        try {
            const valeurs = this.historique.map(h => this.obtenirValeurChemin(h.metriques, chemin))
                .filter(v => v !== undefined && !isNaN(v));
            
            if (valeurs.length === 0) return 0;
            
            return valeurs.reduce((sum, val) => sum + val, 0) / valeurs.length;
        } catch (error) {
            return 0;
        }
    }

    // CALCULER DISPONIBILITÉ
    calculerDisponibilite(service) {
        try {
            const etatsOllama = this.historique.map(h => h.metriques.ollama.actif);
            const actifs = etatsOllama.filter(Boolean).length;
            
            return etatsOllama.length > 0 ? (actifs / etatsOllama.length) * 100 : 0;
        } catch (error) {
            return 0;
        }
    }

    // OBTENIR VALEUR CHEMIN
    obtenirValeurChemin(obj, chemin) {
        return chemin.split('.').reduce((o, p) => o && o[p], obj);
    }

    // GÉNÉRER RECOMMANDATIONS
    genererRecommandations() {
        const recommandations = [];
        
        if (this.metriques.systeme.cpu.utilisation > 80) {
            recommandations.push('Optimiser utilisation CPU - considérer réduction charge');
        }
        
        if (this.metriques.systeme.memoire.pourcentage > 85) {
            recommandations.push('Libérer mémoire - nettoyer cache ou redémarrer services');
        }
        
        if (this.metriques.ollama.erreurs > 5) {
            recommandations.push('Vérifier configuration Ollama - trop d\'erreurs détectées');
        }
        
        if (this.metriques.agent.connexions_actives < 5) {
            recommandations.push('Améliorer connexions agent - plusieurs modules déconnectés');
        }
        
        if (recommandations.length === 0) {
            recommandations.push('Système fonctionnel - aucune action requise');
        }
        
        return recommandations;
    }

    // OBTENIR STATUT COMPLET
    obtenirStatutComplet() {
        return {
            metriques: this.metriques,
            alertes: this.alertes.slice(0, 20), // 20 dernières alertes
            historique_recent: this.historique.slice(-60), // Dernière heure
            seuils: this.seuils,
            statistiques: {
                uptime: process.uptime(),
                alertes_total: this.alertes.length,
                alertes_critiques: this.alertes.filter(a => a.niveau === 'critique').length,
                historique_taille: this.historique.length
            },
            timestamp: new Date().toISOString()
        };
    }

    // RÉSOUDRE ALERTE
    resoudreAlerte(alerteId) {
        const alerte = this.alertes.find(a => a.id === alerteId);
        if (alerte) {
            alerte.resolu = true;
            alerte.resolu_timestamp = new Date().toISOString();
            return true;
        }
        return false;
    }

    genererIdUnique() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
}

module.exports = MonitoringTempsReel;
