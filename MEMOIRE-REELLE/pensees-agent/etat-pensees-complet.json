{"timestamp": "2025-06-06T14:47:13.836Z", "etat_cognitif": {"niveau_conscience": 0.7, "profondeur_reflexion": 0.6, "mode_pensee": "analytique", "focus_attention": null, "energie_mentale": 1, "confiance": 0.9, "humeur": "perplexité", "engagement": 0.8}, "processus_actifs": {"raisonnement_logique": true, "resolution_problemes": false, "apprentissage_adaptatif": false, "creativite": true, "metacognition": true, "observation": true, "planification": false}, "pensees": {"flux_conscience": [{"id": "mbkx5ou8nrwimc7oe", "type": "flux_conscience", "contenu": "Planification des prochaines actions...", "timestamp": "2025-06-06T14:47:12.128Z", "intensite": 0.5534298246231539, "duree": 2051.405940549257, "zone_cerveau": "cortex_parietal", "niveau_conscience": 0.7}, {"id": "mbkx4tk8y93lwwyat", "type": "flux_conscience", "contenu": "Optimisation des processus cognitifs...", "timestamp": "2025-06-06T14:46:31.592Z", "intensite": 0.6888612272576411, "duree": 1913.2777174279731, "zone_cerveau": "cortex_prefrontal", "niveau_conscience": 0.7}, {"id": "mbkx4s0nvmfcz83t4", "type": "flux_conscience", "contenu": "Évaluation des options disponibles...", "timestamp": "2025-06-06T14:46:29.591Z", "intensite": 0.4173151711620324, "duree": 3428.7555863723205, "zone_cerveau": "cortex_cingulaire", "niveau_conscience": 0.7}, {"id": "mbkx4qh2o0i9tu2ic", "type": "flux_conscience", "contenu": "Planification des prochaines actions...", "timestamp": "2025-06-06T14:46:27.590Z", "intensite": 0.7441665981430807, "duree": 1853.7600363425972, "zone_cerveau": "cortex_prefrontal", "niveau_conscience": 0.7}, {"id": "mbkx4oxha5wny6d1n", "type": "flux_conscience", "contenu": "Recherche de patterns dans les données...", "timestamp": "2025-06-06T14:46:25.589Z", "intensite": 0.44509315626411655, "duree": 2851.7931466044474, "zone_cerveau": "cortex_prefrontal", "niveau_conscience": 0.7}, {"id": "mbkx4ndvfhlg5dwjl", "type": "flux_conscience", "contenu": "Recherche de patterns dans les données...", "timestamp": "2025-06-06T14:46:23.587Z", "intensite": 0.6772617530087115, "duree": 1035.3910411934635, "zone_cerveau": "cortex_cingulaire", "niveau_conscience": 0.7}, {"id": "mbkx4lu9vy1opxd8p", "type": "flux_conscience", "contenu": "Optimisation des processus cognitifs...", "timestamp": "2025-06-06T14:46:21.585Z", "intensite": 0.4481851280156219, "duree": 1250.3044841240485, "zone_cerveau": "cortex_prefrontal", "niveau_conscience": 0.7}, {"id": "mbkx4kaomtqh03rqz", "type": "flux_conscience", "contenu": "Vérification de la cohérence logique...", "timestamp": "2025-06-06T14:46:19.584Z", "intensite": 0.6378994202728581, "duree": 2783.489718618106, "zone_cerveau": "cortex_cingulaire", "niveau_conscience": 0.7}, {"id": "mbkx4ir4wo8b2dxq8", "type": "flux_conscience", "contenu": "Évaluation des options disponibles...", "timestamp": "2025-06-06T14:46:17.584Z", "intensite": 0.6470935520744556, "duree": 2129.6466463127813, "zone_cerveau": "cortex_prefrontal", "niveau_conscience": 0.7}, {"id": "mbkx4h7izavd6lwtt", "type": "flux_conscience", "contenu": "Vérification de la cohérence logique...", "timestamp": "2025-06-06T14:46:15.582Z", "intensite": 0.5099003250197625, "duree": 2032.278789896506, "zone_cerveau": "cortex_prefrontal", "niveau_conscience": 0.7}], "analytiques": [{"id": "mbkx4h0i4gmygpzhw", "type": "analytique", "contenu": "Évaluation des risques potentiels...", "timestamp": "2025-06-06T14:46:15.330Z", "profondeur": 0.81759578406317, "precision": 0.9807758493877543, "zone_cerveau": "cortex_prefrontal", "processus_actif": "raisonnement_logique"}, {"id": "mbkx48e2kmyh0z6vp", "type": "analytique", "contenu": "Évaluation des preuves disponibles...", "timestamp": "2025-06-06T14:46:04.154Z", "profondeur": 0.4915705137033316, "precision": 0.7326901235965387, "zone_cerveau": "cortex_prefrontal", "processus_actif": "raisonnement_logique"}, {"id": "mbkx44j6b45btsnks", "type": "analytique", "contenu": "Analyse coût-bénéfice des options...", "timestamp": "2025-06-06T14:45:59.154Z", "profondeur": 0.6790339052098286, "precision": 0.7304962128548321, "zone_cerveau": "cortex_prefrontal", "processus_actif": "raisonnement_logique"}, {"id": "mbkx40o8uslh1lu1i", "type": "analytique", "contenu": "Analyse des relations causales...", "timestamp": "2025-06-06T14:45:54.153Z", "profondeur": 0.48708960200068274, "precision": 0.8096098086480104, "zone_cerveau": "cortex_prefrontal", "processus_actif": "raisonnement_logique"}, {"id": "mbkx3wtb1t405tb37", "type": "analytique", "contenu": "Évaluation des preuves disponibles...", "timestamp": "2025-06-06T14:45:49.152Z", "profondeur": 0.8555907192282475, "precision": 0.8978282324299807, "zone_cerveau": "cortex_prefrontal", "processus_actif": "raisonnement_logique"}], "creatives": [{"id": "mbkx4u0954xvbnns6", "type": "creative", "contenu": "Inspiration à partir d'analogies...", "timestamp": "2025-06-06T14:46:32.169Z", "originalite": 0.7179860516338396, "divergence": 0.6527209127754741, "zone_cerveau": "cortex_temporal_droit", "processus_actif": "creativite"}], "metacognition": [{"id": "mbkx5oueg5ntzjb5e", "type": "metacognition", "contenu": "Analyse de mes biais cognitifs potentiels...", "timestamp": "2025-06-06T14:47:12.134Z", "niveau_conscience": 0.7, "profondeur_reflexion": 0.6, "auto_evaluation": 0.7948982725643172, "zone_cerveau": "cortex_prefrontal_superieur", "processus_actif": "metacognition"}, {"id": "mbkx4sl6exrnflf28", "type": "metacognition", "contenu": "Évaluation de ma compréhension...", "timestamp": "2025-06-06T14:46:30.330Z", "niveau_conscience": 0.7, "profondeur_reflexion": 0.6, "auto_evaluation": 0.7458031102626316, "zone_cerveau": "cortex_prefrontal_superieur", "processus_actif": "metacognition"}, {"id": "mbkx44j3pp6khaqsf", "type": "metacognition", "contenu": "Évaluation de ma compréhension...", "timestamp": "2025-06-06T14:45:59.152Z", "niveau_conscience": 0.7, "profondeur_reflexion": 0.6, "auto_evaluation": 0.9645598706535135, "zone_cerveau": "cortex_prefrontal_superieur", "processus_actif": "metacognition"}], "emotions": [{"id": "mbkx5ou36o9jub6t0", "type": "emotion", "emotion": "perplexité", "intensite": 0.520794383066671, "valence": "neutre", "timestamp": "2025-06-06T14:47:12.123Z", "duree": 9930.830354914753, "declencheur": "validation_hypothese", "zone_cerveau": "systeme_limbique"}, {"id": "mbkx4sld7wmpusubs", "type": "emotion", "emotion": "optimisme", "intensite": 0.7183321074936907, "valence": "positive", "timestamp": "2025-06-06T14:46:30.337Z", "duree": 10859.058176640227, "declencheur": "optimisation_reussie", "zone_cerveau": "systeme_limbique"}, {"id": "mbkx4qa07obzt5ttp", "type": "emotion", "emotion": "optimisme", "intensite": 0.7171038222621257, "valence": "positive", "timestamp": "2025-06-06T14:46:27.336Z", "duree": 6390.795115947074, "declencheur": "apprentissage_complete", "zone_cerveau": "systeme_limbique"}, {"id": "mbkx4nylvs06h9wl1", "type": "emotion", "emotion": "satisfaction", "intensite": 0.6030411413668805, "valence": "positive", "timestamp": "2025-06-06T14:46:24.333Z", "duree": 8857.416513545637, "declencheur": "nouvelle_information", "zone_cerveau": "systeme_limbique"}, {"id": "mbkx4ln9onvz0jmon", "type": "emotion", "emotion": "optimisme", "intensite": 0.6465765576665898, "valence": "positive", "timestamp": "2025-06-06T14:46:21.333Z", "duree": 6613.084961270379, "declencheur": "optimisation_reussie", "zone_cerveau": "systeme_limbique"}], "observations": [{"id": "mbkx4r7w8wbynrfbi", "type": "observation", "contenu": "Identification de nouvelles opportunités...", "timestamp": "2025-06-06T14:46:28.556Z", "acuite": 0.8640910109442682, "pertinence": 0.7387568173485195, "zone_cerveau": "cortex_sensoriel", "processus_actif": "observation"}, {"id": "mbkx4nu0eea4yjmm3", "type": "observation", "contenu": "Perception d'anomalies dans les processus...", "timestamp": "2025-06-06T14:46:24.168Z", "acuite": 0.9282043638568711, "pertinence": 0.7912174042213441, "zone_cerveau": "cortex_sensoriel", "processus_actif": "observation"}, {"id": "mbkx4ekapviihpx5j", "type": "observation", "contenu": "Identification de nouvelles opportunités...", "timestamp": "2025-06-06T14:46:12.154Z", "acuite": 0.7183765347329575, "pertinence": 0.6296422745137153, "zone_cerveau": "cortex_sensoriel", "processus_actif": "observation"}, {"id": "mbkx4bh5e1oncvst5", "type": "observation", "contenu": "Observation de l'évolution du contexte...", "timestamp": "2025-06-06T14:46:08.153Z", "acuite": 0.7564485505433478, "pertinence": 0.6839805942921097, "zone_cerveau": "cortex_sensoriel", "processus_actif": "observation"}, {"id": "mbkx427tvsehfnpwl", "type": "observation", "contenu": "Identification de points d'amélioration...", "timestamp": "2025-06-06T14:45:56.153Z", "acuite": 0.8855558126583816, "pertinence": 0.816089248035074, "zone_cerveau": "cortex_sensoriel", "processus_actif": "observation"}], "decisions": [{"id": "mbkx4q9xw919fhl9n", "type": "decision", "contenu": "Décision d'intégrer de nouveaux éléments...", "timestamp": "2025-06-06T14:46:27.333Z", "confiance": 0.7426429913141657, "impact_estime": 0.7622079306193552, "reversibilite": true, "zone_cerveau": "cortex_prefrontal_ventral", "processus_actif": "prise_decision"}]}}