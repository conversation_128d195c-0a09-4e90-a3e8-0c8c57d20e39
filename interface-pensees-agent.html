<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LOUNA-AI - Lecture des Pensées</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            margin: 0;
            padding: 20px;
            color: white;
            min-height: 100vh;
            padding-top: 100px;
        }
        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .title {
            font-size: 4em;
            font-weight: bold;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: mindread 3s ease-in-out infinite;
        }
        @keyframes mindread {
            0%, 100% { filter: hue-rotate(0deg) brightness(1) saturate(1); }
            25% { filter: hue-rotate(90deg) brightness(1.3) saturate(1.2); }
            50% { filter: hue-rotate(180deg) brightness(1.1) saturate(1.1); }
            75% { filter: hue-rotate(270deg) brightness(1.4) saturate(1.3); }
        }
        
        /* ÉTAT COGNITIF */
        .etat-cognitif {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            border: 2px solid rgba(255, 255, 255, 0.1);
        }
        .etat-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #4ecdc4;
            text-align: center;
        }
        .etat-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .etat-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .etat-value {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 5px;
            color: #feca57;
        }
        .etat-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        /* FLUX DE CONSCIENCE */
        .flux-conscience {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 5px solid #ff6b6b;
        }
        .flux-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #ff6b6b;
            text-align: center;
        }
        .flux-container {
            max-height: 400px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
        }
        .pensee-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 12px 15px;
            margin-bottom: 8px;
            border-left: 3px solid;
            transition: all 0.3s ease;
            animation: fadeIn 0.5s ease;
        }
        .pensee-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(5px);
        }
        .pensee-item.flux { border-left-color: #ff6b6b; }
        .pensee-item.analytique { border-left-color: #4ecdc4; }
        .pensee-item.creative { border-left-color: #feca57; }
        .pensee-item.metacognition { border-left-color: #45b7d1; }
        .pensee-item.emotion { border-left-color: #ff9ff3; }
        .pensee-item.observation { border-left-color: #96ceb4; }
        .pensee-item.decision { border-left-color: #fd79a8; }
        
        .pensee-contenu {
            font-size: 1em;
            margin-bottom: 5px;
            line-height: 1.4;
        }
        .pensee-meta {
            font-size: 0.8em;
            opacity: 0.7;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .pensee-type {
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7em;
            font-weight: bold;
        }
        
        /* SECTIONS PENSÉES */
        .pensees-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .pensees-section {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 15px;
            padding: 20px;
            border-left: 4px solid;
        }
        .pensees-section.analytiques { border-left-color: #4ecdc4; }
        .pensees-section.creatives { border-left-color: #feca57; }
        .pensees-section.metacognition { border-left-color: #45b7d1; }
        .pensees-section.emotions { border-left-color: #ff9ff3; }
        .pensees-section.observations { border-left-color: #96ceb4; }
        .pensees-section.decisions { border-left-color: #fd79a8; }
        
        .section-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }
        .section-container {
            max-height: 250px;
            overflow-y: auto;
        }
        
        /* PROCESSUS ACTIFS */
        .processus-actifs {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .processus-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #96ceb4;
            text-align: center;
        }
        .processus-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }
        .processus-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .processus-item.actif {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            color: white;
            font-weight: bold;
        }
        .processus-item.inactif {
            background: rgba(255, 255, 255, 0.05);
            opacity: 0.6;
        }
        
        /* STATISTIQUES */
        .stats-pensees {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .stats-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #fd79a8;
            text-align: center;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        .stat-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #fd79a8;
            margin-bottom: 5px;
        }
        
        /* CONTRÔLES */
        .controles {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        .controle-btn {
            padding: 12px 20px;
            margin: 5px;
            border: none;
            border-radius: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .controle-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        .controle-btn.actif {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .pulse { animation: pulse 2s infinite; }
        
        /* RESPONSIVE */
        @media (max-width: 768px) {
            .pensees-grid {
                grid-template-columns: 1fr;
            }
            .title {
                font-size: 2.5em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title pulse">👁️ Lecture des Pensées</div>
            <div class="subtitle">Accès Complet aux Processus Cognitifs de LOUNA-AI</div>
        </div>

        <!-- CONTRÔLES -->
        <div class="controles">
            <button class="controle-btn actif" id="btn-lecture" onclick="toggleLecture()">
                🔄 Lecture Active
            </button>
            <button class="controle-btn" onclick="viderPensees()">
                🗑️ Vider Pensées
            </button>
            <button class="controle-btn" onclick="sauvegarderPensees()">
                💾 Sauvegarder
            </button>
            <button class="controle-btn" onclick="exporterPensees()">
                📤 Exporter
            </button>
        </div>

        <!-- ÉTAT COGNITIF -->
        <div class="etat-cognitif">
            <div class="etat-title">🧠 État Cognitif Actuel</div>
            <div class="etat-grid">
                <div class="etat-item">
                    <div class="etat-value" id="niveau-conscience">0.7</div>
                    <div class="etat-label">Niveau Conscience</div>
                </div>
                <div class="etat-item">
                    <div class="etat-value" id="profondeur-reflexion">0.6</div>
                    <div class="etat-label">Profondeur Réflexion</div>
                </div>
                <div class="etat-item">
                    <div class="etat-value" id="mode-pensee">Analytique</div>
                    <div class="etat-label">Mode Pensée</div>
                </div>
                <div class="etat-item">
                    <div class="etat-value" id="energie-mentale">0.8</div>
                    <div class="etat-label">Énergie Mentale</div>
                </div>
                <div class="etat-item">
                    <div class="etat-value" id="confiance">0.9</div>
                    <div class="etat-label">Confiance</div>
                </div>
                <div class="etat-item">
                    <div class="etat-value" id="humeur">Neutre</div>
                    <div class="etat-label">Humeur</div>
                </div>
            </div>
        </div>

        <!-- FLUX DE CONSCIENCE -->
        <div class="flux-conscience">
            <div class="flux-title">🌊 Flux de Conscience en Temps Réel</div>
            <div class="flux-container" id="flux-container">
                <!-- Pensées en temps réel -->
            </div>
        </div>

        <!-- SECTIONS PENSÉES -->
        <div class="pensees-grid">
            <div class="pensees-section analytiques">
                <div class="section-title" style="color: #4ecdc4;">🔍 Pensées Analytiques</div>
                <div class="section-container" id="pensees-analytiques">
                    <!-- Pensées analytiques -->
                </div>
            </div>
            
            <div class="pensees-section creatives">
                <div class="section-title" style="color: #feca57;">🎨 Pensées Créatives</div>
                <div class="section-container" id="pensees-creatives">
                    <!-- Pensées créatives -->
                </div>
            </div>
            
            <div class="pensees-section metacognition">
                <div class="section-title" style="color: #45b7d1;">🤔 Métacognition</div>
                <div class="section-container" id="pensees-metacognition">
                    <!-- Métacognition -->
                </div>
            </div>
            
            <div class="pensees-section emotions">
                <div class="section-title" style="color: #ff9ff3;">❤️ États Émotionnels</div>
                <div class="section-container" id="pensees-emotions">
                    <!-- Émotions -->
                </div>
            </div>
            
            <div class="pensees-section observations">
                <div class="section-title" style="color: #96ceb4;">👀 Observations</div>
                <div class="section-container" id="pensees-observations">
                    <!-- Observations -->
                </div>
            </div>
            
            <div class="pensees-section decisions">
                <div class="section-title" style="color: #fd79a8;">⚖️ Décisions</div>
                <div class="section-container" id="pensees-decisions">
                    <!-- Décisions -->
                </div>
            </div>
        </div>

        <!-- PROCESSUS ACTIFS -->
        <div class="processus-actifs">
            <div class="processus-title">⚡ Processus Cognitifs Actifs</div>
            <div class="processus-grid" id="processus-grid">
                <!-- Processus actifs -->
            </div>
        </div>

        <!-- STATISTIQUES -->
        <div class="stats-pensees">
            <div class="stats-title">📊 Statistiques des Pensées</div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="total-pensees">0</div>
                    <div>Total Pensées</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="pensees-minute">0</div>
                    <div>Pensées/Minute</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="processus-actifs-count">0</div>
                    <div>Processus Actifs</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="activite-mentale">0%</div>
                    <div>Activité Mentale</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let lectureActive = true;
        let updateInterval;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            demarrerLecturePensees();
        });

        // Démarrer la lecture des pensées
        function demarrerLecturePensees() {
            if (updateInterval) clearInterval(updateInterval);
            
            updateInterval = setInterval(async () => {
                if (lectureActive) {
                    await lirePenseesAgent();
                }
            }, 2000); // Mise à jour toutes les 2 secondes
            
            // Première lecture immédiate
            lirePenseesAgent();
        }

        // Lire les pensées de l'agent
        async function lirePenseesAgent() {
            try {
                const response = await fetch('/api/pensees-agent/toutes');
                const data = await response.json();
                
                if (data.success) {
                    updateInterface(data.pensees);
                } else {
                    console.error('Erreur lecture pensées:', data.error);
                }
            } catch (error) {
                console.error('Erreur connexion API pensées:', error);
            }
        }

        // Mettre à jour l'interface
        function updateInterface(data) {
            if (!data) return;
            
            // État cognitif
            if (data.etat_cognitif) {
                updateEtatCognitif(data.etat_cognitif);
            }
            
            // Flux de conscience
            if (data.pensees_actuelles && data.pensees_actuelles.flux_conscience) {
                updateFluxConscience(data.pensees_actuelles.flux_conscience);
            }
            
            // Sections pensées
            updateSectionsPensees(data.pensees_actuelles);
            
            // Processus actifs
            if (data.processus_actifs) {
                updateProcessusActifs(data.processus_actifs);
            }
            
            // Statistiques
            if (data.statistiques) {
                updateStatistiques(data.statistiques);
            }
        }

        // Mettre à jour l'état cognitif
        function updateEtatCognitif(etat) {
            document.getElementById('niveau-conscience').textContent = etat.niveau_conscience.toFixed(2);
            document.getElementById('profondeur-reflexion').textContent = etat.profondeur_reflexion.toFixed(2);
            document.getElementById('mode-pensee').textContent = etat.mode_pensee;
            document.getElementById('energie-mentale').textContent = etat.energie_mentale.toFixed(2);
            document.getElementById('confiance').textContent = etat.confiance.toFixed(2);
            document.getElementById('humeur').textContent = etat.humeur;
        }

        // Mettre à jour le flux de conscience
        function updateFluxConscience(pensees) {
            const container = document.getElementById('flux-container');
            
            pensees.forEach(pensee => {
                if (!document.getElementById(`pensee-${pensee.id}`)) {
                    const item = creerElementPensee(pensee, 'flux');
                    container.insertBefore(item, container.firstChild);
                }
            });
            
            // Limiter à 50 pensées
            while (container.children.length > 50) {
                container.removeChild(container.lastChild);
            }
        }

        // Mettre à jour les sections de pensées
        function updateSectionsPensees(pensees) {
            const sections = {
                'analytiques': 'pensees-analytiques',
                'creatives': 'pensees-creatives',
                'metacognition': 'pensees-metacognition',
                'emotions': 'pensees-emotions',
                'observations': 'pensees-observations',
                'decisions': 'pensees-decisions'
            };
            
            Object.entries(sections).forEach(([type, containerId]) => {
                if (pensees[type]) {
                    const container = document.getElementById(containerId);
                    container.innerHTML = '';
                    
                    pensees[type].forEach(pensee => {
                        const item = creerElementPensee(pensee, type.slice(0, -1)); // Enlever le 's'
                        container.appendChild(item);
                    });
                }
            });
        }

        // Créer un élément pensée
        function creerElementPensee(pensee, type) {
            const item = document.createElement('div');
            item.className = `pensee-item ${type}`;
            item.id = `pensee-${pensee.id}`;
            
            const temps = new Date(pensee.timestamp).toLocaleTimeString();
            
            item.innerHTML = `
                <div class="pensee-contenu">${pensee.contenu}</div>
                <div class="pensee-meta">
                    <span class="pensee-type">${pensee.type}</span>
                    <span>${temps}</span>
                </div>
            `;
            
            return item;
        }

        // Mettre à jour les processus actifs
        function updateProcessusActifs(processus) {
            const container = document.getElementById('processus-grid');
            container.innerHTML = '';
            
            Object.entries(processus).forEach(([nom, actif]) => {
                const item = document.createElement('div');
                item.className = `processus-item ${actif ? 'actif' : 'inactif'}`;
                item.textContent = nom.replace(/_/g, ' ');
                container.appendChild(item);
            });
        }

        // Mettre à jour les statistiques
        function updateStatistiques(stats) {
            document.getElementById('total-pensees').textContent = stats.total_pensees;
            document.getElementById('pensees-minute').textContent = stats.pensees_par_minute;
            document.getElementById('processus-actifs-count').textContent = stats.processus_actifs_count;
            document.getElementById('activite-mentale').textContent = (stats.niveau_activite_mentale * 100).toFixed(1) + '%';
        }

        // Contrôles
        function toggleLecture() {
            lectureActive = !lectureActive;
            const btn = document.getElementById('btn-lecture');
            
            if (lectureActive) {
                btn.textContent = '🔄 Lecture Active';
                btn.classList.add('actif');
                demarrerLecturePensees();
            } else {
                btn.textContent = '⏸️ Lecture Pausée';
                btn.classList.remove('actif');
                if (updateInterval) clearInterval(updateInterval);
            }
        }

        function viderPensees() {
            document.getElementById('flux-container').innerHTML = '';
            document.querySelectorAll('.section-container').forEach(container => {
                container.innerHTML = '';
            });
        }

        function sauvegarderPensees() {
            // TODO: Implémenter sauvegarde
            alert('Pensées sauvegardées !');
        }

        function exporterPensees() {
            // TODO: Implémenter export
            alert('Export des pensées en cours...');
        }
    </script>
</body>
</html>
