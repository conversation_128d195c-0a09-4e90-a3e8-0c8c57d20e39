#!/usr/bin/env node

/**
 * SYSTÈME D'ÉVOLUTION AUTOMATIQUE DU QI
 * Basé sur la neuroplasticité et l'apprentissage adaptatif
 */

const fs = require('fs');
const path = require('path');

class EvolutionQIAutomatique {
    constructor() {
        this.qiBase = 120; // QI initial scientifique
        this.facteurEvolution = 0.1; // Taux d'amélioration par session
        this.maxQI = 200; // Limite théorique
        this.sessionsCount = 0;
        this.dataFile = path.join(__dirname, 'MEMOIRE-REELLE/evolution-qi.json');
        
        this.chargerDonnees();
        this.demarrerEvolution();
    }

    chargerDonnees() {
        try {
            if (fs.existsSync(this.dataFile)) {
                const data = JSON.parse(fs.readFileSync(this.dataFile, 'utf8'));
                this.qiActuel = data.qiActuel || this.qiBase;
                this.sessionsCount = data.sessionsCount || 0;
                this.historique = data.historique || [];
            } else {
                this.qiActuel = this.qiBase;
                this.historique = [];
                this.sauvegarderDonnees();
            }
        } catch (error) {
            console.log('🔄 Initialisation nouvelles données QI...');
            this.qiActuel = this.qiBase;
            this.historique = [];
        }
    }

    sauvegarderDonnees() {
        try {
            const data = {
                qiActuel: this.qiActuel,
                sessionsCount: this.sessionsCount,
                historique: this.historique,
                derniereMiseAJour: new Date().toISOString()
            };
            
            // Créer le dossier si nécessaire
            const dir = path.dirname(this.dataFile);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
            
            fs.writeFileSync(this.dataFile, JSON.stringify(data, null, 2));
        } catch (error) {
            console.log('⚠️ Erreur sauvegarde:', error.message);
        }
    }

    calculerEvolution() {
        // Facteurs d'amélioration basés sur la neuroplasticité
        const facteurs = {
            usage: this.sessionsCount * 0.05, // Plus d'usage = plus d'amélioration
            complexite: Math.log(this.sessionsCount + 1) * 2, // Apprentissage logarithmique
            plasticite: Math.random() * 0.5, // Variabilité naturelle
            consolidation: this.sessionsCount > 10 ? 1 : 0 // Bonus après consolidation
        };

        const amelioration = Object.values(facteurs).reduce((a, b) => a + b, 0);
        const nouveauQI = Math.min(this.qiActuel + amelioration, this.maxQI);
        
        return Math.round(nouveauQI * 100) / 100; // Arrondi à 2 décimales
    }

    evoluer() {
        const ancienQI = this.qiActuel;
        this.qiActuel = this.calculerEvolution();
        this.sessionsCount++;
        
        // Enregistrer dans l'historique
        this.historique.push({
            session: this.sessionsCount,
            qiAvant: ancienQI,
            qiApres: this.qiActuel,
            amelioration: this.qiActuel - ancienQI,
            timestamp: new Date().toISOString()
        });

        // Garder seulement les 100 dernières entrées
        if (this.historique.length > 100) {
            this.historique = this.historique.slice(-100);
        }

        this.sauvegarderDonnees();
        return this.qiActuel;
    }

    obtenirStatistiques() {
        const ameliorationTotale = this.qiActuel - this.qiBase;
        const moyenneAmelioration = this.historique.length > 0 
            ? this.historique.reduce((sum, h) => sum + h.amelioration, 0) / this.historique.length
            : 0;

        return {
            qiActuel: this.qiActuel,
            qiBase: this.qiBase,
            ameliorationTotale,
            sessionsCount: this.sessionsCount,
            moyenneAmelioration,
            progression: ((this.qiActuel - this.qiBase) / (this.maxQI - this.qiBase)) * 100
        };
    }

    demarrerEvolution() {
        console.log('🧠 ========================================');
        console.log('⚡ SYSTÈME D\'ÉVOLUTION QI AUTOMATIQUE');
        console.log('🧠 ========================================');
        console.log('');

        // Évolution automatique toutes les 30 secondes
        setInterval(() => {
            const nouveauQI = this.evoluer();
            const stats = this.obtenirStatistiques();
            
            console.log(`🧠 QI Évolution: ${stats.qiActuel} (+${stats.ameliorationTotale.toFixed(2)} depuis le début)`);
            console.log(`📊 Session: ${stats.sessionsCount} | Progression: ${stats.progression.toFixed(1)}%`);
            
            // Mettre à jour l'interface si elle existe
            this.mettreAJourInterface(stats.qiActuel);
            
        }, 30000); // 30 secondes

        // Affichage initial
        const stats = this.obtenirStatistiques();
        console.log('✅ Système d\'évolution QI démarré !');
        console.log(`🧠 QI Initial: ${this.qiBase}`);
        console.log(`🧠 QI Actuel: ${stats.qiActuel}`);
        console.log(`📊 Sessions: ${stats.sessionsCount}`);
        console.log('');
    }

    mettreAJourInterface(nouveauQI) {
        // Mettre à jour le fichier server.js avec le nouveau QI
        try {
            const serverPath = path.join(__dirname, 'server.js');
            if (fs.existsSync(serverPath)) {
                let content = fs.readFileSync(serverPath, 'utf8');
                
                // Remplacer le QI dans le code
                content = content.replace(
                    /qi: \d+(\.\d+)?/g, 
                    `qi: ${Math.round(nouveauQI)}`
                );
                
                // Remplacer dans l'affichage HTML
                content = content.replace(
                    /id="qi-memoire">\d+(\.\d+)?</g,
                    `id="qi-memoire">${Math.round(nouveauQI)}<`
                );
                
                fs.writeFileSync(serverPath, content);
            }
        } catch (error) {
            // Erreur silencieuse pour ne pas interrompre l'évolution
        }
    }

    // API pour obtenir le QI actuel
    obtenirQIActuel() {
        return this.qiActuel;
    }
}

// Démarrer le système
const evolutionQI = new EvolutionQIAutomatique();

// Export pour utilisation dans d'autres modules
module.exports = evolutionQI;

// Si lancé directement
if (require.main === module) {
    console.log('🚀 Système d\'évolution QI en cours d\'exécution...');
    console.log('🛑 Appuyez sur Ctrl+C pour arrêter');
    
    // Garder le processus actif
    process.on('SIGINT', () => {
        console.log('\n🛑 Arrêt du système d\'évolution QI');
        process.exit(0);
    });
}
