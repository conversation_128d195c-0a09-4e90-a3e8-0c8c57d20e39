const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const axios = require('axios');
const { promisify } = require('util');
const fs = require('fs');

// Configuration
const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  },
  transports: ['websocket', 'polling']
});
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Configuration du moteur de template
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// URL de l'API Ollama
const OLLAMA_API_URL = 'http://localhost:11434/api';

// Route principale - utiliser un template statique pour éviter les problèmes
app.get('/', (req, res) => {
  res.render('index-simple', {
    title: 'DeepSeek r1 - Version Minimale'
  });
});

// Gestion des connexions Socket.io
io.on('connection', (socket) => {
  console.log('New client connected with ID:', socket.id);

  // Vérification immédiate d'Ollama
  testOllamaConnection(socket);

  // Vérification périodique d'Ollama (toutes les 10 secondes)
  const interval = setInterval(() => {
    testOllamaConnection(socket);
  }, 10000);

  // Gestionnaire pour les messages de chat
  socket.on('chat message', async (data) => {
    try {
      console.log('Received message from client:', data);
      const modelName = data.modelName || 'deepseek-r1:7b'; // Utiliser le modèle disponible
      
      socket.emit('processing', true);
      
      // Préparer la requête pour l'API Ollama
      const requestData = {
        model: modelName,
        prompt: data.message,
        options: {
          temperature: parseFloat(data.temperature || 0.7),
          num_predict: parseInt(data.maxTokens || 1000)
        }
      };
      
      console.log('Sending request to Ollama:', JSON.stringify(requestData));
      
      // Appeler l'API Ollama
      const response = await axios.post(`${OLLAMA_API_URL}/generate`, requestData);
      const botResponse = response.data.response;
      
      // Envoyer la réponse formatée
      socket.emit('chat response', {
        message: {
          role: 'assistant',
          content: botResponse
        }
      });
      
      console.log('Sent response to client');
      socket.emit('processing', false);
    } catch (error) {
      console.error('Error handling chat message:', error.message);
      
      socket.emit('chat response', {
        message: {
          role: 'assistant',
          content: `Erreur: ${error.message}`
        }
      });
      
      socket.emit('processing', false);
    }
  });

  // Gestion de la vérification d'Ollama
  socket.on('check ollama', () => {
    testOllamaConnection(socket);
  });

  // Nettoyage lors de la déconnexion
  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
    clearInterval(interval);
  });
});

// Fonction pour tester la connexion à Ollama
async function testOllamaConnection(socket) {
  try {
    console.log('Testing Ollama connection...');
    const response = await axios.get(`${OLLAMA_API_URL}/version`);
    console.log('Ollama is available:', response.data);
    
    socket.emit('ollama status', {
      available: true,
      version: response.data.version
    });
    
    // Récupérer les modèles disponibles
    try {
      const modelsResponse = await axios.get(`${OLLAMA_API_URL}/tags`);
      const models = modelsResponse.data.models || [];
      socket.emit('models', models);
    } catch (modelsError) {
      console.error('Error fetching models:', modelsError.message);
    }
  } catch (error) {
    console.error('Ollama is not available:', error.message);
    socket.emit('ollama status', {
      available: false,
      error: error.message
    });
  }
}

// Démarrer le serveur
server.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
  console.log('Minimal version of DeepSeek r1 server started');
});
