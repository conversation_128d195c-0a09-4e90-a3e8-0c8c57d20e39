/**
 * SYSTÈME DE NAVIGATION COMPLÈTE
 * Liens entre toutes les interfaces LOUNA-AI
 */

function creerNavigationComplete() {
    return `
    <style>
        /* NAVIGATION GLOBALE */
        .navigation-globale {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, rgba(0,0,0,0.9) 0%, rgba(30,30,30,0.95) 100%);
            backdrop-filter: blur(15px);
            border-bottom: 2px solid rgba(255,255,255,0.1);
            z-index: 1000;
            padding: 10px 0;
        }
        
        .nav-container {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .nav-logo {
            font-size: 1.5em;
            font-weight: bold;
            background: linear-gradient(45deg, #3498db, #2ecc71, #f39c12, #e74c3c);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-decoration: none;
        }
        
        .nav-links {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 20px;
            background: rgba(255,255,255,0.1);
            transition: all 0.3s ease;
            font-size: 0.9em;
            border: 1px solid transparent;
        }
        
        .nav-link:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .nav-link.active {
            background: linear-gradient(45deg, #3498db, #2ecc71);
            color: white;
        }
        
        /* MENU MOBILE */
        .nav-toggle {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 1.5em;
            cursor: pointer;
        }
        
        @media (max-width: 768px) {
            .nav-toggle {
                display: block;
            }
            
            .nav-links {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: rgba(0,0,0,0.95);
                flex-direction: column;
                padding: 20px;
                gap: 10px;
            }
            
            .nav-links.active {
                display: flex;
            }
            
            .nav-link {
                text-align: center;
                padding: 12px 20px;
            }
        }
        
        /* AJUSTEMENT CONTENU */
        body {
            padding-top: 80px !important;
        }
        
        /* INDICATEUR PAGE ACTIVE */
        .page-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            z-index: 999;
        }
        
        /* RACCOURCIS CLAVIER */
        .shortcuts-info {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.8em;
            z-index: 999;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }
        
        .shortcuts-info:hover {
            opacity: 1;
        }
    </style>
    
    <nav class="navigation-globale">
        <div class="nav-container">
            <a href="/" class="nav-logo">🧠 LOUNA-AI</a>
            
            <button class="nav-toggle" onclick="toggleNavigation()">☰</button>
            
            <div class="nav-links" id="nav-links">
                <a href="/" class="nav-link" title="Interface principale avec indicateurs">
                    🏠 Accueil
                </a>
                <a href="/agent" class="nav-link" title="Gestion et contrôle de l'agent">
                    🤖 Agent
                </a>
                <a href="/apprentissage" class="nav-link" title="Système d'apprentissage et évolution QI">
                    📚 Apprentissage
                </a>
                <a href="/cerveau" class="nav-link" title="Activité cérébrale complète avec pourcentages">
                    🧠 Cerveau
                </a>
                <a href="/memoire" class="nav-link" title="Mémoire thermique réelle connectée">
                    🔥 Mémoire
                </a>
                <a href="/chat" class="nav-link" title="Chat avancé avec Ollama">
                    💬 Chat
                </a>
                <a href="/dashboard" class="nav-link" title="Tableau de bord système">
                    📊 Dashboard
                </a>
                <a href="/monitoring" class="nav-link" title="Monitoring temps réel">
                    📈 Monitoring
                </a>
                <a href="/api-docs" class="nav-link" title="Documentation des APIs">
                    🔧 APIs
                </a>
            </div>
        </div>
    </nav>
    
    <div class="page-indicator" id="page-indicator">
        📍 Page Actuelle
    </div>
    
    <div class="shortcuts-info">
        ⌨️ Raccourcis: Alt**** pour navigation rapide
    </div>
    
    <script>
        // NAVIGATION MOBILE
        function toggleNavigation() {
            const navLinks = document.getElementById('nav-links');
            navLinks.classList.toggle('active');
        }
        
        // MARQUER PAGE ACTIVE
        function marquerPageActive() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link');
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
            
            // Mettre à jour indicateur
            const pageNames = {
                '/': 'Interface Principale',
                '/agent': 'Gestion Agent',
                '/apprentissage': 'Apprentissage & Évolution',
                '/cerveau': 'Activité Cérébrale',
                '/memoire': 'Mémoire Thermique',
                '/chat': 'Chat Avancé',
                '/dashboard': 'Dashboard',
                '/monitoring': 'Monitoring',
                '/api-docs': 'Documentation APIs'
            };
            
            const indicator = document.getElementById('page-indicator');
            indicator.textContent = '📍 ' + (pageNames[currentPath] || 'Page Inconnue');
        }
        
        // RACCOURCIS CLAVIER
        document.addEventListener('keydown', function(e) {
            if (e.altKey) {
                const shortcuts = {
                    '1': '/',
                    '2': '/agent',
                    '3': '/apprentissage',
                    '4': '/cerveau',
                    '5': '/memoire',
                    '6': '/chat',
                    '7': '/dashboard',
                    '8': '/monitoring',
                    '9': '/api-docs'
                };
                
                if (shortcuts[e.key]) {
                    e.preventDefault();
                    window.location.href = shortcuts[e.key];
                }
            }
        });
        
        // INITIALISATION
        document.addEventListener('DOMContentLoaded', function() {
            marquerPageActive();
            
            // Fermer menu mobile en cliquant ailleurs
            document.addEventListener('click', function(e) {
                const navLinks = document.getElementById('nav-links');
                const navToggle = document.querySelector('.nav-toggle');
                
                if (!navLinks.contains(e.target) && !navToggle.contains(e.target)) {
                    navLinks.classList.remove('active');
                }
            });
        });
        
        // MISE À JOUR AUTOMATIQUE STATUT
        setInterval(function() {
            // Vérifier si on est toujours sur la bonne page
            marquerPageActive();
        }, 5000);
    </script>
    `;
}

// FONCTION POUR AJOUTER LA NAVIGATION À UNE PAGE
function ajouterNavigationAPage(contenuHTML) {
    const navigation = creerNavigationComplete();
    
    // Insérer la navigation après la balise <body>
    return contenuHTML.replace(
        /<body([^>]*)>/i,
        `<body$1>${navigation}`
    );
}

// LIENS RAPIDES POUR DÉVELOPPEMENT
function creerLiensRapides() {
    return `
    <div style="position: fixed; top: 100px; right: 20px; background: rgba(0,0,0,0.8); color: white; padding: 15px; border-radius: 10px; z-index: 998; font-size: 0.8em;">
        <div style="font-weight: bold; margin-bottom: 10px;">🔗 Liens Rapides</div>
        <div><a href="/api/status-ultra-complet" target="_blank" style="color: #3498db;">Status Système</a></div>
        <div><a href="/api/agent/status" target="_blank" style="color: #2ecc71;">Status Agent</a></div>
        <div><a href="/api/activite-cerebrale/complete" target="_blank" style="color: #f39c12;">Activité Cérébrale</a></div>
        <div><a href="/api/apprentissage/status" target="_blank" style="color: #e74c3c;">Apprentissage</a></div>
        <div><a href="/api/memoire-thermique/status" target="_blank" style="color: #9b59b6;">Mémoire Thermique</a></div>
    </div>
    `;
}

module.exports = {
    creerNavigationComplete,
    ajouterNavigationAPage,
    creerLiensRapides
};
