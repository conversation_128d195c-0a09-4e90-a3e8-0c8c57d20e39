{"version": 3, "file": "main.js", "sourceRoot": "", "sources": ["../src/main.ts"], "names": [], "mappings": ";;;AAAA,2BAAmC;AACnC,qCAA8B;AAC9B,6BAA4B;AAE5B,mCAA0C;AAC1C,mDAA6C;AAO7C,KAAK,UAAU,UAAU,CAAI,UAAkB,EAAE,OAA0B;IACzE,MAAM,IAAI,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;IAClD,IAAI,MAAM,CAAA;IACV,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;QACjE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;KACtC;SACI,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;QAClE,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,CAAA;QAC5B,IAAI,MAAM,CAAC,OAAO,IAAI,IAAI,EAAE;YAC1B,MAAM,GAAG,MAAM,CAAC,OAAO,CAAA;SACxB;QACD,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;YAChC,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;SACzB;QACD,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;KACvC;SACI,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACnC,MAAM,GAAG,6BAAY,CAAI,UAAU,CAAC,CAAA;QACpC,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;YAChC,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;SACzB;QACD,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;KACvC;SACI,IAAI,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;QACrC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;KACrC;SACI;QACH,MAAM,GAAG,cAAI,CAAC,IAAI,CAAC,CAAA;KACpB;IACD,OAAO,EAAC,MAAM,EAAE,UAAU,EAAC,CAAA;AAC7B,CAAC;AAEM,KAAK,UAAU,iBAAiB,CAAI,OAA0B;IACnE,MAAM,MAAM,GAAG,OAAO,CAAC,cAAc,CAAA;IACrC,KAAK,MAAM,UAAU,IAAI,CAAC,GAAG,MAAM,MAAM,EAAE,GAAG,MAAM,OAAO,EAAE,GAAG,MAAM,OAAO,EAAE,GAAG,MAAM,QAAQ,EAAE,GAAG,MAAM,OAAO,EAAE,GAAG,MAAM,KAAK,EAAE,GAAG,MAAM,MAAM,EAAE,GAAG,MAAM,KAAK,CAAC,EAAE;QACpK,MAAM,IAAI,GAAG,MAAM,oBAAoB,CAAC,UAAU,CAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,OAAO,CAAC,CAAC,CAAA;QAC1G,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,OAAO,IAAI,CAAA;SACZ;KACF;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAVD,8CAUC;AAED,SAAgB,oBAAoB,CAAI,OAAmB;IACzD,OAAO,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;AACxC,CAAC;AAFD,oDAEC;AAED,SAAgB,gBAAgB,CAAI,OAAmB,EAAE,aAAgB;IACvE,OAAO,OAAO;SACX,KAAK,CAAC,CAAC,CAAC,EAAE;QACT,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,SAAS,EAAE;YAC/C,OAAO,aAAa,CAAA;SACrB;QACD,MAAM,CAAC,CAAA;IACT,CAAC,CAAC,CAAA;AACN,CAAC;AARD,4CAQC;AAUM,KAAK,UAAU,UAAU,CAAI,OAA0B;IAC5D,IAAI,eAAe,GAAG,OAAO,CAAC,eAAe,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,OAAO,CAAC,eAAe,CAAC,KAAK,CAAA;IAClG,IAAI,eAAe,IAAI,IAAI,EAAE;QAC3B,MAAM,IAAI,GAAG,MAAM,oBAAoB,CAAC,aAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,cAAc,CAAC,EAAE,MAAM,CAAC,CAAC,CAAA;QAC3G,eAAe,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;KACzD;IAED,MAAM,IAAI,GAAM,eAAe,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;IACpF,OAAO,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAI,OAAO,CAAC,CAAC,CAAC,CAAC,EAAC,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAC,CAAA;AACxF,CAAC;AATD,gCASC;AAED,SAAgB,SAAS,CAAI,OAA0B,EAAE,UAA0B;IACjF,IAAI,UAAU,IAAI,IAAI,EAAE;QACtB,OAAO,UAAU,CAAI,OAAO,CAAC,CAAA;KAC9B;SACI;QACH,OAAO,UAAU,CAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,OAAO,CAAC,CAAA;KAC5E;AACH,CAAC;AAPD,8BAOC;AAEM,KAAK,UAAU,gBAAgB,CAAI,OAA0B,EAAE,IAAY;IAChF,IAAI,UAA+B,CAAA;IACnC,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;QAC5B,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;QACrC,UAAU,GAAG,IAAI,CAAA;KAClB;IAED,IAAI,YAAY,GAAG,MAAM,oBAAoB,CAAC,UAAU,CAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,CAAA;IAC7G,IAAI,YAAY,IAAI,IAAI,IAAI,UAAU,KAAK,IAAI,EAAE;QAC/C,IAAI,QAAQ,GAAkB,IAAI,CAAA;QAClC,IAAI;YACF,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;SACjC;QACD,OAAO,CAAC,EAAE;YACR,SAAS;SACV;QAED,IAAI,QAAQ,IAAI,IAAI,EAAE;YACpB,YAAY,GAAG,MAAM,UAAU,CAAI,QAAQ,EAAE,OAAO,CAAC,CAAA;SACtD;KACF;IAED,IAAI,YAAY,IAAI,IAAI,EAAE;QACxB,MAAM,IAAI,KAAK,CAAC,mCAAmC,IAAI,EAAE,CAAC,CAAA;KAC3D;IAED,OAAO,YAAY,CAAA;AACrB,CAAC;AA3BD,4CA2BC;AAEM,KAAK,UAAU,OAAO,CAAC,OAAe;IAC3C,MAAM,IAAI,GAAG,MAAM,oBAAoB,CAAC,aAAE,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAA;IACrE,IAAI,IAAI,IAAI,IAAI,EAAE;QAChB,OAAO,IAAI,CAAA;KACZ;IAED,MAAM,MAAM,GAAG,cAAQ,CAAC,IAAI,CAAC,CAAA;IAC7B,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;QACrC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YACpC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;SAC/B;KACF;IACD,OAAO,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,CAAA;IAChC,OAAO,MAAM,CAAA;AACf,CAAC;AAdD,0BAcC", "sourcesContent": ["import { promises as fs } from \"fs\"\nimport { load } from \"js-yaml\"\nimport * as path from \"path\"\nimport { Lazy } from \"lazy-val\"\nimport { parse as parseEnv } from \"dotenv\"\nimport { loadTsConfig } from \"config-file-ts\"\n\nexport interface ReadConfigResult<T> {\n  readonly result: T\n  readonly configFile: string | null\n}\n\nasync function readConfig<T>(configFile: string, request: ReadConfigRequest): Promise<ReadConfigResult<T>> {\n  const data = await fs.readFile(configFile, \"utf8\")\n  let result\n  if (configFile.endsWith(\".json5\") || configFile.endsWith(\".json\")) {\n    result = require(\"json5\").parse(data)\n  }\n  else if (configFile.endsWith(\".js\") || configFile.endsWith(\".cjs\")) {\n    result = require(configFile)\n    if (result.default != null) {\n      result = result.default\n    }\n    if (typeof result === \"function\") {\n      result = result(request)\n    }\n    result = await Promise.resolve(result)\n  }\n  else if (configFile.endsWith(\".ts\")) {\n    result = loadTsConfig<T>(configFile)\n    if (typeof result === \"function\") {\n      result = result(request)\n    }\n    result = await Promise.resolve(result)\n  }\n  else if (configFile.endsWith(\".toml\")) {\n    result = require(\"toml\").parse(data)\n  }\n  else {\n    result = load(data)\n  }\n  return {result, configFile}\n}\n\nexport async function findAndReadConfig<T>(request: ReadConfigRequest): Promise<ReadConfigResult<T> | null> {\n  const prefix = request.configFilename\n  for (const configFile of [`${prefix}.yml`, `${prefix}.yaml`, `${prefix}.json`, `${prefix}.json5`, `${prefix}.toml`, `${prefix}.js`, `${prefix}.cjs`, `${prefix}.ts`]) {\n    const data = await orNullIfFileNotExist(readConfig<T>(path.join(request.projectDir, configFile), request))\n    if (data != null) {\n      return data\n    }\n  }\n\n  return null\n}\n\nexport function orNullIfFileNotExist<T>(promise: Promise<T>): Promise<T | null> {\n  return orIfFileNotExist(promise, null)\n}\n\nexport function orIfFileNotExist<T>(promise: Promise<T>, fallbackValue: T): Promise<T> {\n  return promise\n    .catch(e => {\n      if (e.code === \"ENOENT\" || e.code === \"ENOTDIR\") {\n        return fallbackValue\n      }\n      throw e\n    })\n}\n\nexport interface ReadConfigRequest {\n  packageKey: string\n  configFilename: string\n\n  projectDir: string\n  packageMetadata: Lazy<{ [key: string]: any } | null> | null\n}\n\nexport async function loadConfig<T>(request: ReadConfigRequest): Promise<ReadConfigResult<T> | null> {\n  let packageMetadata = request.packageMetadata == null ? null : await request.packageMetadata.value\n  if (packageMetadata == null) {\n    const json = await orNullIfFileNotExist(fs.readFile(path.join(request.projectDir, \"package.json\"), \"utf8\"))\n    packageMetadata = json == null ? null : JSON.parse(json)\n  }\n\n  const data: T = packageMetadata == null ? null : packageMetadata[request.packageKey]\n  return data == null ? findAndReadConfig<T>(request) : {result: data, configFile: null}\n}\n\nexport function getConfig<T>(request: ReadConfigRequest, configPath?: string | null): Promise<ReadConfigResult<T> | null> {\n  if (configPath == null) {\n    return loadConfig<T>(request)\n  }\n  else {\n    return readConfig<T>(path.resolve(request.projectDir, configPath), request)\n  }\n}\n\nexport async function loadParentConfig<T>(request: ReadConfigRequest, spec: string): Promise<ReadConfigResult<T>> {\n  let isFileSpec: boolean | undefined\n  if (spec.startsWith(\"file:\")) {\n    spec = spec.substring(\"file:\".length)\n    isFileSpec = true\n  }\n\n  let parentConfig = await orNullIfFileNotExist(readConfig<T>(path.resolve(request.projectDir, spec), request))\n  if (parentConfig == null && isFileSpec !== true) {\n    let resolved: string | null = null\n    try {\n      resolved = require.resolve(spec)\n    }\n    catch (e) {\n      // ignore\n    }\n\n    if (resolved != null) {\n      parentConfig = await readConfig<T>(resolved, request)\n    }\n  }\n\n  if (parentConfig == null) {\n    throw new Error(`Cannot find parent config file: ${spec}`)\n  }\n\n  return parentConfig\n}\n\nexport async function loadEnv(envFile: string) {\n  const data = await orNullIfFileNotExist(fs.readFile(envFile, \"utf8\"))\n  if (data == null) {\n    return null\n  }\n\n  const parsed = parseEnv(data)\n  for (const key of Object.keys(parsed)) {\n    if (!process.env.hasOwnProperty(key)) {\n      process.env[key] = parsed[key]\n    }\n  }\n  require(\"dotenv-expand\")(parsed)\n  return parsed\n}\n"]}