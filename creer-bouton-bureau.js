#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class CreerBoutonBureau {
    constructor() {
        console.log('🖥️ CRÉATION BOUTON LANCEMENT BUREAU');
        console.log('===================================');
        console.log('🎯 Raccourci pour lancer LOUNA-AI facilement');
        
        this.cheminBureau = path.join(require('os').homedir(), 'Desktop');
        this.cheminProjet = process.cwd();
        
        console.log(`📁 Bureau: ${this.cheminBureau}`);
        console.log(`📂 Projet: ${this.cheminProjet}`);
    }
    
    async creerBoutonLancement() {
        console.log('\n🚀 CRÉATION DU BOUTON DE LANCEMENT');
        console.log('==================================');
        
        try {
            // C<PERSON>er le script de lancement
            await this.creerScriptLancement();
            
            // Créer l'icône
            await this.creerIcone();
            
            // Créer le raccourci sur le bureau
            await this.creerRaccourciBureau();
            
            // Rendre exécutable
            await this.rendreExecutable();
            
            console.log('\n✅ BOUTON CRÉÉ AVEC SUCCÈS !');
            console.log('🖥️ Vérifiez votre bureau pour le raccourci LOUNA-AI');
            
        } catch (error) {
            console.log(`❌ Erreur: ${error.message}`);
        }
    }
    
    async creerScriptLancement() {
        console.log('📝 Création script de lancement...');
        
        const scriptContent = `#!/bin/bash

# Script de lancement LOUNA-AI
# Créé automatiquement

echo "🧠 LANCEMENT LOUNA-AI"
echo "===================="
echo "🎯 Intelligence Artificielle Avancée"
echo ""

# Aller dans le bon répertoire
cd "${this.cheminProjet}"

echo "📁 Répertoire: $(pwd)"
echo "🔍 Vérification des composants..."

# Vérifier Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js non installé"
    echo "💡 Installez Node.js depuis https://nodejs.org"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

echo "✅ Node.js: $(node --version)"

# Vérifier Ollama
if ! command -v ollama &> /dev/null; then
    echo "⚠️ Ollama non installé"
    echo "💡 Installez Ollama depuis https://ollama.ai"
else
    echo "✅ Ollama installé"
fi

# Vérifier si Ollama est en cours d'exécution
if curl -s http://localhost:11434/api/version > /dev/null 2>&1; then
    echo "✅ Ollama en cours d'exécution"
else
    echo "🚀 Démarrage d'Ollama..."
    ollama serve &
    sleep 3
fi

echo ""
echo "🧠 Lancement de l'interface LOUNA-AI..."
echo ""

# Lancer l'interface principale
if [ -f "AGENT_LOCAL_COMPLET/louna-interface-complete.js" ]; then
    echo "🎯 Lancement interface complète..."
    cd AGENT_LOCAL_COMPLET
    node louna-interface-complete.js
elif [ -f "server.js" ]; then
    echo "🎯 Lancement serveur principal..."
    node server.js
else
    echo "❌ Fichier de lancement non trouvé"
    echo "📁 Fichiers disponibles:"
    ls -la *.js 2>/dev/null || echo "Aucun fichier .js trouvé"
    echo ""
    echo "💡 Vérifiez que vous êtes dans le bon répertoire"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

echo ""
echo "🛑 LOUNA-AI arrêté"
read -p "Appuyez sur Entrée pour fermer..."`;

        const scriptPath = path.join(this.cheminBureau, 'Lancer-LOUNA-AI.command');
        fs.writeFileSync(scriptPath, scriptContent);
        
        console.log(`✅ Script créé: ${scriptPath}`);
        return scriptPath;
    }
    
    async creerIcone() {
        console.log('🎨 Création de l\'icône...');
        
        // Créer une icône simple en ASCII art pour l'affichage
        const iconeContent = `#!/bin/bash
# Icône LOUNA-AI
echo "🧠"`;
        
        const iconePath = path.join(this.cheminBureau, '.louna-icon.sh');
        fs.writeFileSync(iconePath, iconeContent);
        
        console.log('✅ Icône créée');
        return iconePath;
    }
    
    async creerRaccourciBureau() {
        console.log('🔗 Création raccourci bureau...');
        
        const scriptPath = path.join(this.cheminBureau, 'Lancer-LOUNA-AI.command');
        
        // Sur macOS, créer un alias/raccourci
        if (process.platform === 'darwin') {
            try {
                // Créer un AppleScript pour le raccourci
                const applescriptContent = `tell application "Terminal"
    activate
    do script "cd '${this.cheminProjet}' && bash '${scriptPath}'"
end tell`;
                
                const applescriptPath = path.join(this.cheminBureau, 'LOUNA-AI.scpt');
                fs.writeFileSync(applescriptPath, applescriptContent);
                
                // Compiler en application
                try {
                    execSync(`osacompile -o "${this.cheminBureau}/🧠 LOUNA-AI.app" "${applescriptPath}"`);
                    console.log('✅ Application macOS créée');
                    
                    // Supprimer le fichier .scpt temporaire
                    fs.unlinkSync(applescriptPath);
                } catch (error) {
                    console.log('⚠️ Impossible de créer l\'app, utilisation du script .command');
                }
                
            } catch (error) {
                console.log(`⚠️ Erreur AppleScript: ${error.message}`);
            }
        }
        
        console.log('✅ Raccourci créé');
    }
    
    async rendreExecutable() {
        console.log('🔧 Configuration permissions...');
        
        const scriptPath = path.join(this.cheminBureau, 'Lancer-LOUNA-AI.command');
        
        try {
            // Rendre le script exécutable
            execSync(`chmod +x "${scriptPath}"`);
            console.log('✅ Script rendu exécutable');
        } catch (error) {
            console.log(`⚠️ Erreur permissions: ${error.message}`);
        }
    }
    
    async creerRaccourciAlternatif() {
        console.log('🔄 Création raccourci alternatif...');
        
        // Créer un fichier HTML qui lance l'application
        const htmlContent = `<!DOCTYPE html>
<html>
<head>
    <title>🧠 LOUNA-AI</title>
    <meta charset="utf-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 50px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        h1 {
            font-size: 3em;
            margin-bottom: 20px;
        }
        .launch-btn {
            background: #4CAF50;
            color: white;
            padding: 20px 40px;
            font-size: 1.5em;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            margin: 20px;
            transition: all 0.3s;
        }
        .launch-btn:hover {
            background: #45a049;
            transform: scale(1.05);
        }
        .info {
            margin-top: 30px;
            font-size: 1.1em;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 LOUNA-AI</h1>
        <p>Intelligence Artificielle Avancée</p>
        
        <button class="launch-btn" onclick="lancerLounaAI()">
            🚀 Lancer LOUNA-AI
        </button>
        
        <button class="launch-btn" onclick="ouvrirInterface()">
            🖥️ Interface Web
        </button>
        
        <div class="info">
            <p>📁 Projet: ${this.cheminProjet}</p>
            <p>🎯 QI: Évolutif • 🧠 Mémoire: Thermique • ⚡ Accélérateurs: KYBER</p>
        </div>
    </div>
    
    <script>
        function lancerLounaAI() {
            alert('🚀 Lancement de LOUNA-AI...\\n\\n1. Ouvrez Terminal\\n2. Tapez: cd "${this.cheminProjet}"\\n3. Tapez: node server.js');
        }
        
        function ouvrirInterface() {
            window.open('http://localhost:3000', '_blank');
        }
        
        // Auto-refresh pour vérifier si le serveur est en ligne
        setInterval(() => {
            fetch('http://localhost:3000')
                .then(() => {
                    document.body.style.borderTop = '5px solid #4CAF50';
                })
                .catch(() => {
                    document.body.style.borderTop = '5px solid #f44336';
                });
        }, 5000);
    </script>
</body>
</html>`;
        
        const htmlPath = path.join(this.cheminBureau, '🧠 LOUNA-AI Launcher.html');
        fs.writeFileSync(htmlPath, htmlContent);
        
        console.log(`✅ Lanceur HTML créé: ${htmlPath}`);
    }
    
    async afficherInstructions() {
        console.log('\n📋 INSTRUCTIONS D\'UTILISATION');
        console.log('=============================');
        console.log('');
        console.log('🖥️ Sur votre bureau, vous trouverez :');
        console.log('');
        console.log('1. 📄 "Lancer-LOUNA-AI.command"');
        console.log('   → Double-cliquez pour lancer via Terminal');
        console.log('');
        console.log('2. 🍎 "🧠 LOUNA-AI.app" (si macOS)');
        console.log('   → Double-cliquez pour lancer l\'application');
        console.log('');
        console.log('3. 🌐 "🧠 LOUNA-AI Launcher.html"');
        console.log('   → Ouvrez dans votre navigateur');
        console.log('');
        console.log('🎯 UTILISATION :');
        console.log('• Double-cliquez sur l\'icône de votre choix');
        console.log('• LOUNA-AI se lancera automatiquement');
        console.log('• L\'interface s\'ouvrira dans votre navigateur');
        console.log('');
        console.log('🔧 DÉPANNAGE :');
        console.log('• Si erreur "Permission denied" → Clic droit → "Ouvrir avec" → Terminal');
        console.log('• Si Ollama non trouvé → Installez depuis https://ollama.ai');
        console.log('• Si Node.js non trouvé → Installez depuis https://nodejs.org');
        console.log('');
        console.log('✅ LOUNA-AI est maintenant accessible depuis votre bureau !');
    }
}

// Lancement de la création
if (require.main === module) {
    const createur = new CreerBoutonBureau();
    
    createur.creerBoutonLancement()
        .then(() => {
            return createur.creerRaccourciAlternatif();
        })
        .then(() => {
            return createur.afficherInstructions();
        })
        .then(() => {
            console.log('\n🎉 BOUTON DE LANCEMENT CRÉÉ !');
            console.log('🖥️ Vérifiez votre bureau');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n❌ Erreur:', error.message);
            process.exit(1);
        });
}

module.exports = CreerBoutonBureau;
