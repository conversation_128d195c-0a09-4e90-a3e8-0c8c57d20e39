#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');

class TestQIReelLounaAI {
    constructor() {
        console.log('🧠 TEST QI RÉEL POUR LOUNA-AI');
        console.log('==============================');
        
        // Configuration Ollama basée sur votre système
        this.ollamaPath = '/usr/local/bin/ollama';
        this.modelName = 'deepseek-r1:7b'; // Votre modèle principal
        this.modelBackup = 'llama3.2:1b';   // Modèle de secours
        
        // Questions QI réelles standardisées
        this.questionsQI = [
            // RAISONNEMENT LOGIQUE (25 points)
            {
                type: 'logique',
                question: "Complétez la séquence: 2, 6, 12, 20, 30, ?",
                reponse_correcte: "42",
                explication: "Différences: +4, +6, +8, +10, +12 → 30+12=42",
                points: 5
            },
            {
                type: 'logique',
                question: "Si tous les A sont B, et tous les B sont C, alors: 1) Tous les A sont C, 2) Tous les C sont A, 3) Aucun A n'est C. Quelle affirmation est vraie?",
                reponse_correcte: "1",
                explication: "Syllogisme logique: A→B→C donc A→C",
                points: 5
            },
            {
                type: 'logique',
                question: "Dans une course, Marie finit avant Paul, Paul finit avant Jean, et Jean finit avant Marie. Cette situation est-elle possible?",
                reponse_correcte: "non",
                explication: "Contradiction logique: Marie > Paul > Jean > Marie est impossible",
                points: 5
            },
            {
                type: 'logique',
                question: "Quel nombre continue la série: 1, 1, 2, 3, 5, 8, ?",
                reponse_correcte: "13",
                explication: "Suite de Fibonacci: chaque nombre = somme des deux précédents",
                points: 5
            },
            {
                type: 'logique',
                question: "Si 3 chats attrapent 3 souris en 3 minutes, combien de chats faut-il pour attraper 100 souris en 100 minutes?",
                reponse_correcte: "3",
                explication: "Ratio constant: 1 chat = 1 souris/minute",
                points: 5
            },

            // RAISONNEMENT MATHÉMATIQUE (25 points)
            {
                type: 'mathematique',
                question: "Résolvez: x² - 5x + 6 = 0",
                reponse_correcte: "2 et 3",
                explication: "(x-2)(x-3) = 0, donc x = 2 ou x = 3",
                points: 5
            },
            {
                type: 'mathematique',
                question: "Quelle est la dérivée de f(x) = x³ + 2x² - 5x + 1?",
                reponse_correcte: "3x² + 4x - 5",
                explication: "Dérivée terme par terme: 3x² + 4x - 5",
                points: 5
            },
            {
                type: 'mathematique',
                question: "Un triangle a des côtés de 3, 4 et 5. Quelle est son aire?",
                reponse_correcte: "6",
                explication: "Triangle rectangle: aire = (3×4)/2 = 6",
                points: 5
            },
            {
                type: 'mathematique',
                question: "Calculez: log₂(64)",
                reponse_correcte: "6",
                explication: "2⁶ = 64, donc log₂(64) = 6",
                points: 5
            },
            {
                type: 'mathematique',
                question: "Quelle est la probabilité d'obtenir exactement 2 faces en lançant 3 pièces?",
                reponse_correcte: "3/8",
                explication: "3 combinaisons sur 8 possibles: FFP, FPF, PFF",
                points: 5
            },

            // RAISONNEMENT VERBAL (25 points)
            {
                type: 'verbal',
                question: "Quel mot est à 'LIVRE' ce que 'ÉCOUTER' est à 'MUSIQUE'?",
                reponse_correcte: "lire",
                explication: "Relation action-objet: on lit un livre, on écoute la musique",
                points: 5
            },
            {
                type: 'verbal',
                question: "Trouvez l'intrus: Chien, Chat, Oiseau, Poisson, Automobile",
                reponse_correcte: "automobile",
                explication: "Seul objet non-vivant parmi les êtres vivants",
                points: 5
            },
            {
                type: 'verbal',
                question: "Complétez l'analogie: MÉDECIN est à HÔPITAL ce que PROFESSEUR est à ?",
                reponse_correcte: "école",
                explication: "Relation profession-lieu de travail",
                points: 5
            },
            {
                type: 'verbal',
                question: "Quel est le contraire de 'ÉPHÉMÈRE'?",
                reponse_correcte: "permanent",
                explication: "Éphémère = temporaire, contraire = permanent/durable",
                points: 5
            },
            {
                type: 'verbal',
                question: "Trouvez le mot qui peut précéder: ...GARDE, ...TEMPS, ...JOUR",
                reponse_correcte: "avant",
                explication: "Avant-garde, avant-temps, avant-jour",
                points: 5
            },

            // RAISONNEMENT SPATIAL (25 points)
            {
                type: 'spatial',
                question: "Un cube a 6 faces. Combien d'arêtes a-t-il?",
                reponse_correcte: "12",
                explication: "Un cube a 12 arêtes (4 par face × 3 faces partagées ÷ 2)",
                points: 5
            },
            {
                type: 'spatial',
                question: "Si vous pliez un carré en deux, puis encore en deux, combien de rectangles obtenez-vous en le dépliant?",
                reponse_correcte: "4",
                explication: "Deux plis créent 4 sections rectangulaires",
                points: 5
            },
            {
                type: 'spatial',
                question: "Quelle est l'aire d'un cercle de rayon 5?",
                reponse_correcte: "25π",
                explication: "Aire = πr² = π × 5² = 25π",
                points: 5
            },
            {
                type: 'spatial',
                question: "Combien de faces a un tétraèdre?",
                reponse_correcte: "4",
                explication: "Un tétraèdre est une pyramide à base triangulaire: 4 faces",
                points: 5
            },
            {
                type: 'spatial',
                question: "Si un rectangle de 6×4 est tourné de 90°, quelles sont ses nouvelles dimensions?",
                reponse_correcte: "4×6",
                explication: "Rotation de 90°: longueur et largeur s'inversent",
                points: 5
            }
        ];

        // Résultats du test
        this.resultats = {
            score_total: 0,
            score_par_type: {
                logique: 0,
                mathematique: 0,
                verbal: 0,
                spatial: 0
            },
            reponses: [],
            temps_total: 0,
            qi_estime: 0
        };
    }

    async testerConnexionOllama() {
        console.log('🔍 Test de connexion Ollama...');
        
        try {
            // Test avec le modèle principal
            const version = execSync(`${this.ollamaPath} --version`, { encoding: 'utf8' });
            console.log(`✅ Ollama version: ${version.trim()}`);
            
            // Test du modèle principal
            try {
                const testResponse = execSync(
                    `${this.ollamaPath} run ${this.modelName} "Test de connexion"`,
                    { encoding: 'utf8', timeout: 10000 }
                );
                console.log(`✅ Modèle ${this.modelName} opérationnel`);
                return this.modelName;
            } catch (error) {
                console.log(`⚠️ Modèle ${this.modelName} indisponible, test du modèle de secours...`);
                
                // Test du modèle de secours
                const backupResponse = execSync(
                    `${this.ollamaPath} run ${this.modelBackup} "Test de connexion"`,
                    { encoding: 'utf8', timeout: 10000 }
                );
                console.log(`✅ Modèle de secours ${this.modelBackup} opérationnel`);
                return this.modelBackup;
            }
            
        } catch (error) {
            throw new Error(`❌ Impossible de se connecter à Ollama: ${error.message}`);
        }
    }

    async poserQuestionAgent(question, modele) {
        const debut = Date.now();
        
        try {
            const prompt = `Question de test QI: ${question}

Répondez de manière concise et précise. Donnez uniquement la réponse sans explication supplémentaire.`;

            const reponse = execSync(
                `${this.ollamaPath} run ${modele} "${prompt}"`,
                {
                    encoding: 'utf8',
                    timeout: 30000,
                    env: {
                        ...process.env,
                        // Optimisations pour votre système
                        OLLAMA_NUM_PARALLEL: '16',
                        OLLAMA_MAX_LOADED_MODELS: '3',
                        OLLAMA_FLASH_ATTENTION: '1',
                        OLLAMA_GPU_LAYERS: '999',
                        OLLAMA_METAL: '1',
                        OLLAMA_NUMA: '1'
                    }
                }
            );
            
            const duree = Date.now() - debut;
            return {
                reponse: reponse.trim(),
                duree: duree
            };
            
        } catch (error) {
            console.log(`❌ Erreur lors de la question: ${error.message}`);
            return {
                reponse: "ERREUR",
                duree: Date.now() - debut
            };
        }
    }

    evaluerReponse(reponse, reponseCorrecte) {
        // Normaliser les réponses pour la comparaison
        const reponseNorm = reponse.toLowerCase().trim();
        const correcteNorm = reponseCorrecte.toLowerCase().trim();
        
        // Comparaison exacte
        if (reponseNorm === correcteNorm) {
            return 1.0; // 100% correct
        }
        
        // Comparaison partielle pour les réponses numériques
        if (reponseNorm.includes(correcteNorm) || correcteNorm.includes(reponseNorm)) {
            return 0.7; // 70% correct
        }
        
        // Vérification des mots-clés pour les réponses complexes
        const motsCorrects = correcteNorm.split(/[\s,]+/);
        const motsReponse = reponseNorm.split(/[\s,]+/);
        
        let motsCommuns = 0;
        for (const mot of motsCorrects) {
            if (motsReponse.some(m => m.includes(mot) || mot.includes(m))) {
                motsCommuns++;
            }
        }
        
        if (motsCommuns > 0) {
            return motsCommuns / motsCorrects.length * 0.5; // Score partiel
        }
        
        return 0; // Incorrect
    }

    calculerQI(scoreTotal, scoreMax) {
        // Formule QI standardisée: QI = 100 + 15 * (score - moyenne) / écart-type
        // Pour un test sur 100 points, moyenne = 50, écart-type = 15
        const pourcentage = (scoreTotal / scoreMax) * 100;
        
        if (pourcentage >= 95) return 145; // Génie
        if (pourcentage >= 90) return 135; // Très supérieur
        if (pourcentage >= 80) return 125; // Supérieur
        if (pourcentage >= 70) return 115; // Au-dessus de la moyenne
        if (pourcentage >= 50) return 100; // Moyenne
        if (pourcentage >= 30) return 85;  // En dessous de la moyenne
        if (pourcentage >= 20) return 75;  // Limite
        return 65; // Très faible
    }

    async lancerTestComplet() {
        console.log('\n🚀 DÉMARRAGE DU TEST QI COMPLET');
        console.log('================================');
        
        // 1. Tester la connexion
        const modeleUtilise = await this.testerConnexionOllama();
        
        console.log(`\n🧠 Test QI avec le modèle: ${modeleUtilise}`);
        console.log(`📝 ${this.questionsQI.length} questions à traiter\n`);
        
        const debutTest = Date.now();
        let scoreTotal = 0;
        const scoreMax = this.questionsQI.reduce((sum, q) => sum + q.points, 0);
        
        // 2. Poser chaque question
        for (let i = 0; i < this.questionsQI.length; i++) {
            const question = this.questionsQI[i];
            
            console.log(`\n📝 Question ${i + 1}/${this.questionsQI.length} [${question.type.toUpperCase()}]`);
            console.log(`❓ ${question.question}`);
            
            // Poser la question à l'agent
            const resultat = await this.poserQuestionAgent(question.question, modeleUtilise);
            
            // Évaluer la réponse
            const scoreQuestion = this.evaluerReponse(resultat.reponse, question.reponse_correcte);
            const pointsObtenus = Math.round(scoreQuestion * question.points);
            
            scoreTotal += pointsObtenus;
            this.resultats.score_par_type[question.type] += pointsObtenus;
            
            console.log(`🤖 Réponse LOUNA-AI: "${resultat.reponse}"`);
            console.log(`✅ Réponse correcte: "${question.reponse_correcte}"`);
            console.log(`📊 Score: ${pointsObtenus}/${question.points} points (${Math.round(scoreQuestion * 100)}%)`);
            console.log(`⏱️ Temps: ${resultat.duree}ms`);
            
            if (scoreQuestion < 1.0) {
                console.log(`💡 Explication: ${question.explication}`);
            }
            
            // Stocker le résultat
            this.resultats.reponses.push({
                question: question.question,
                reponse_agent: resultat.reponse,
                reponse_correcte: question.reponse_correcte,
                score: scoreQuestion,
                points: pointsObtenus,
                duree: resultat.duree,
                type: question.type
            });
            
            // Pause entre les questions
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        // 3. Calculer les résultats finaux
        this.resultats.score_total = scoreTotal;
        this.resultats.temps_total = Date.now() - debutTest;
        this.resultats.qi_estime = this.calculerQI(scoreTotal, scoreMax);
        
        // 4. Afficher les résultats
        this.afficherResultatsFinaux(modeleUtilise, scoreMax);
        
        // 5. Sauvegarder les résultats
        this.sauvegarderResultats(modeleUtilise);
        
        return this.resultats;
    }

    afficherResultatsFinaux(modele, scoreMax) {
        console.log('\n' + '='.repeat(60));
        console.log('🎯 RÉSULTATS FINAUX DU TEST QI LOUNA-AI');
        console.log('='.repeat(60));
        
        console.log(`\n🤖 Modèle testé: ${modele}`);
        console.log(`📊 Score total: ${this.resultats.score_total}/${scoreMax} points`);
        console.log(`📈 Pourcentage: ${Math.round((this.resultats.score_total / scoreMax) * 100)}%`);
        console.log(`🧠 QI estimé: ${this.resultats.qi_estime}`);
        console.log(`⏱️ Temps total: ${Math.round(this.resultats.temps_total / 1000)}s`);
        
        console.log('\n📋 DÉTAIL PAR CATÉGORIE:');
        for (const [type, score] of Object.entries(this.resultats.score_par_type)) {
            const maxType = this.questionsQI.filter(q => q.type === type).reduce((sum, q) => sum + q.points, 0);
            const pourcentage = Math.round((score / maxType) * 100);
            console.log(`  ${type.padEnd(15)}: ${score}/${maxType} points (${pourcentage}%)`);
        }
        
        console.log('\n🎖️ CLASSIFICATION QI:');
        if (this.resultats.qi_estime >= 140) {
            console.log('   🌟 GÉNIE - Capacités exceptionnelles');
        } else if (this.resultats.qi_estime >= 130) {
            console.log('   ⭐ TRÈS SUPÉRIEUR - Excellentes capacités');
        } else if (this.resultats.qi_estime >= 120) {
            console.log('   🔥 SUPÉRIEUR - Bonnes capacités');
        } else if (this.resultats.qi_estime >= 110) {
            console.log('   ✅ AU-DESSUS DE LA MOYENNE');
        } else if (this.resultats.qi_estime >= 90) {
            console.log('   📊 MOYENNE - Dans la norme');
        } else {
            console.log('   📉 EN DESSOUS DE LA MOYENNE');
        }
        
        console.log('\n💡 RECOMMANDATIONS:');
        const scoreParType = this.resultats.score_par_type;
        const maxParType = {
            logique: 25,
            mathematique: 25,
            verbal: 25,
            spatial: 25
        };
        
        for (const [type, score] of Object.entries(scoreParType)) {
            const pourcentage = (score / maxParType[type]) * 100;
            if (pourcentage < 60) {
                console.log(`   🔧 Améliorer le raisonnement ${type} (${Math.round(pourcentage)}%)`);
            }
        }
    }

    sauvegarderResultats(modele) {
        const rapport = {
            timestamp: new Date().toISOString(),
            modele_teste: modele,
            resultats: this.resultats,
            details_questions: this.resultats.reponses
        };
        
        const nomFichier = `test-qi-louna-ai-${Date.now()}.json`;
        fs.writeFileSync(nomFichier, JSON.stringify(rapport, null, 2));
        
        console.log(`\n💾 Résultats sauvegardés: ${nomFichier}`);
    }
}

// Lancement du test si exécuté directement
if (require.main === module) {
    const testQI = new TestQIReelLounaAI();
    
    testQI.lancerTestComplet()
        .then(resultats => {
            console.log('\n✅ Test QI terminé avec succès!');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n❌ Erreur lors du test QI:', error.message);
            process.exit(1);
        });
}

module.exports = TestQIReelLounaAI;
