{
  "name": "http-proxy-agent",
  "version": "7.0.2",
  "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP",
  "main": "./dist/index.js",
  "types": "./dist/index.d.ts",
  "files": [
    "dist"
  ],
  "repository": {
    "type": "git",
    "url": "https://github.com/TooTallNate/proxy-agents.git",
    "directory": "packages/http-proxy-agent"
  },
  "keywords": [
    "http",
    "proxy",
    "endpoint",
    "agent"
  ],
  "author": "<PERSON> <<EMAIL>> (http://n8.io/)",
