<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LOUNA-AI - Tableau de Bord Avancé</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            margin: 0;
            padding: 20px;
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .title {
            font-size: 3.5em;
            font-weight: bold;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #FFD700, #FFA500, #FF6B6B, #4ECDC4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradient 3s ease-in-out infinite;
        }
        @keyframes gradient {
            0%, 100% { filter: hue-rotate(0deg); }
            50% { filter: hue-rotate(90deg); }
        }
        
        /* NAVIGATION */
        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 10px;
        }
        .nav-tab {
            padding: 12px 24px;
            margin: 0 5px;
            border: none;
            border-radius: 10px;
            background: transparent;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        .nav-tab:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        .nav-tab.active {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: black;
            font-weight: bold;
        }
        
        /* SECTIONS */
        .section {
            display: none;
            animation: fadeIn 0.5s ease-in-out;
        }
        .section.active {
            display: block;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* DASHBOARD GRID */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .dashboard-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        .card-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #FFD700;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .card-content {
            font-size: 1.1em;
        }
        .metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .metric-value {
            font-weight: bold;
            color: #4ECDC4;
        }
        
        /* INDICATEURS SYSTÈME */
        .system-indicators {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .indicator {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        .indicator.online {
            border-color: #4CAF50;
            background: rgba(76, 175, 80, 0.2);
        }
        .indicator.warning {
            border-color: #FF9800;
            background: rgba(255, 152, 0, 0.2);
        }
        .indicator.offline {
            border-color: #F44336;
            background: rgba(244, 67, 54, 0.2);
        }
        .indicator-light {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin: 0 auto 10px;
            animation: pulse 2s infinite;
        }
        .indicator-light.green { background: #4CAF50; box-shadow: 0 0 15px #4CAF50; }
        .indicator-light.orange { background: #FF9800; box-shadow: 0 0 15px #FF9800; }
        .indicator-light.red { background: #F44336; box-shadow: 0 0 15px #F44336; }
        
        /* GRAPHIQUES */
        .chart-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        .chart-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #FFD700;
        }
        .progress-bar {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            height: 20px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            border-radius: 10px;
            transition: width 1s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 0.9em;
        }
        
        /* CONVERSATIONS */
        .conversation-list {
            max-height: 400px;
            overflow-y: auto;
        }
        .conversation-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .conversation-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(5px);
        }
        .conversation-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .conversation-meta {
            font-size: 0.9em;
            opacity: 0.7;
        }
        
        /* BOUTONS */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        .btn.danger {
            background: linear-gradient(45deg, #FF6B6B, #EE5A52);
        }
        .btn.warning {
            background: linear-gradient(45deg, #FFD93D, #FF9800);
            color: black;
        }
        
        /* RESPONSIVE */
        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            .system-indicators {
                grid-template-columns: repeat(2, 1fr);
            }
            .title {
                font-size: 2.5em;
            }
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">🧠 LOUNA-AI</div>
            <div class="subtitle">Tableau de Bord Avancé - Gestion Complète du Système</div>
        </div>

        <!-- NAVIGATION -->
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showSection('dashboard')">📊 Dashboard</button>
            <button class="nav-tab" onclick="showSection('system')">🔧 Système</button>
            <button class="nav-tab" onclick="showSection('conversations')">💬 Conversations</button>
            <button class="nav-tab" onclick="showSection('memory')">🧠 Mémoire</button>
            <button class="nav-tab" onclick="showSection('settings')">⚙️ Paramètres</button>
        </div>

        <!-- LIENS RAPIDES VERS INTERFACES SPÉCIALISÉES -->
        <div style="text-align: center; margin-bottom: 20px;">
            <a href="/pensees" class="btn" style="text-decoration: none;">👁️ Pensées Agent</a>
            <a href="/memoire-thermique" class="btn" style="text-decoration: none;">🌡️ Mémoire Thermique</a>
            <a href="/systemes" class="btn" style="text-decoration: none;">🔒 VPN + MCP + Scanner</a>
            <a href="/chat" class="btn" style="text-decoration: none;">💬 Chat Avancé</a>
        </div>

        <!-- SECTION DASHBOARD -->
        <div id="dashboard" class="section active">
            <div class="system-indicators">
                <div class="indicator online" id="ollama-indicator">
                    <div class="indicator-light green"></div>
                    <div><strong>Ollama Agent</strong></div>
                    <div>Connecté</div>
                </div>
                <div class="indicator online" id="mcp-indicator">
                    <div class="indicator-light green"></div>
                    <div><strong>MCP Sécurisé</strong></div>
                    <div>Port 3002</div>
                </div>
                <div class="indicator online" id="vpn-indicator">
                    <div class="indicator-light green"></div>
                    <div><strong>VPN Sécurisé</strong></div>
                    <div>AES-256</div>
                </div>
                <div class="indicator online" id="scanner-indicator">
                    <div class="indicator-light green"></div>
                    <div><strong>Scanner Apps</strong></div>
                    <div>31 détectées</div>
                </div>
            </div>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-title">🧠 Intelligence Artificielle</div>
                    <div class="card-content">
                        <div class="metric">
                            <span>QI Total:</span>
                            <span class="metric-value" id="qi-total">226</span>
                        </div>
                        <div class="metric">
                            <span>QI Agent:</span>
                            <span class="metric-value">76</span>
                        </div>
                        <div class="metric">
                            <span>QI Mémoire Thermique:</span>
                            <span class="metric-value">150</span>
                        </div>
                        <div class="metric">
                            <span>Efficacité:</span>
                            <span class="metric-value">94%</span>
                        </div>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-title">🔥 Mémoire Thermique</div>
                    <div class="card-content">
                        <div class="metric">
                            <span>Neurones Totaux:</span>
                            <span class="metric-value">201,207,600</span>
                        </div>
                        <div class="metric">
                            <span>Synapses Totales:</span>
                            <span class="metric-value">1,911,472,200</span>
                        </div>
                        <div class="metric">
                            <span>Température Globale:</span>
                            <span class="metric-value">52.5°C</span>
                        </div>
                        <div class="metric">
                            <span>Zones Actives:</span>
                            <span class="metric-value">6/6</span>
                        </div>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-title">📊 Performance Système</div>
                    <div class="card-content">
                        <div class="metric">
                            <span>CPU:</span>
                            <span class="metric-value" id="cpu-usage">25%</span>
                        </div>
                        <div class="metric">
                            <span>RAM:</span>
                            <span class="metric-value" id="ram-usage">78%</span>
                        </div>
                        <div class="metric">
                            <span>Température CPU:</span>
                            <span class="metric-value" id="cpu-temp">42°C</span>
                        </div>
                        <div class="metric">
                            <span>Uptime:</span>
                            <span class="metric-value" id="uptime">2h 34m</span>
                        </div>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-title">💬 Activité Conversations</div>
                    <div class="card-content">
                        <div class="metric">
                            <span>Conversations Totales:</span>
                            <span class="metric-value" id="total-conversations">0</span>
                        </div>
                        <div class="metric">
                            <span>Messages Totaux:</span>
                            <span class="metric-value" id="total-messages">0</span>
                        </div>
                        <div class="metric">
                            <span>Dernière Activité:</span>
                            <span class="metric-value" id="last-activity">-</span>
                        </div>
                        <div class="metric">
                            <span>Modes Utilisés:</span>
                            <span class="metric-value" id="modes-used">4</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="chart-container">
                <div class="chart-title">📈 Performance en Temps Réel</div>
                <div style="margin-bottom: 10px;">CPU Usage</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="cpu-bar" style="width: 25%;">25%</div>
                </div>
                <div style="margin-bottom: 10px;">RAM Usage</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="ram-bar" style="width: 78%;">78%</div>
                </div>
                <div style="margin-bottom: 10px;">Efficacité IA</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="ai-bar" style="width: 94%;">94%</div>
                </div>
            </div>
        </div>

        <!-- SECTION SYSTÈME -->
        <div id="system" class="section">
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-title">🔧 Actions Système</div>
                    <div class="card-content">
                        <button class="btn" onclick="redemarrerOllama()">🔄 Redémarrer Ollama</button>
                        <button class="btn" onclick="testerConnexions()">🔍 Tester Connexions</button>
                        <button class="btn warning" onclick="creerSauvegarde()">💾 Sauvegarde Complète</button>
                        <button class="btn danger" onclick="reinitialiserSysteme()">⚠️ Réinitialiser</button>
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-title">📊 Diagnostics Détaillés</div>
                    <div class="card-content" id="diagnostics-detail">
                        <div class="metric">
                            <span>Dernier Test:</span>
                            <span class="metric-value" id="last-diagnostic">-</span>
                        </div>
                        <div class="metric">
                            <span>Problèmes Détectés:</span>
                            <span class="metric-value" id="problems-count">0</span>
                        </div>
                        <div class="metric">
                            <span>Performance Globale:</span>
                            <span class="metric-value" id="global-performance">Excellent</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SECTION CONVERSATIONS -->
        <div id="conversations" class="section">
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-title">💬 Gestion Conversations</div>
                    <div class="card-content">
                        <button class="btn" onclick="nouvelleConversation()">➕ Nouvelle Conversation</button>
                        <button class="btn" onclick="exporterConversations()">📤 Exporter Tout</button>
                        <button class="btn" onclick="rechercherConversations()">🔍 Rechercher</button>
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-title">📋 Conversations Récentes</div>
                    <div class="conversation-list" id="conversations-list">
                        <div class="conversation-item">
                            <div class="conversation-title">Chargement...</div>
                            <div class="conversation-meta">Récupération des conversations...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SECTION MÉMOIRE -->
        <div id="memory" class="section">
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-title">🔥 Zones Thermiques Détaillées</div>
                    <div class="card-content" id="thermal-zones">
                        <!-- Sera rempli dynamiquement -->
                    </div>
                </div>
            </div>
        </div>

        <!-- SECTION PARAMÈTRES -->
        <div id="settings" class="section">
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-title">⚙️ Configuration Système</div>
                    <div class="card-content">
                        <div class="metric">
                            <span>Mode Debug:</span>
                            <button class="btn" onclick="toggleDebug()">Activer</button>
                        </div>
                        <div class="metric">
                            <span>Sauvegarde Auto:</span>
                            <button class="btn" onclick="toggleAutoSave()">Activée</button>
                        </div>
                        <div class="metric">
                            <span>Notifications:</span>
                            <button class="btn" onclick="toggleNotifications()">Activées</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let updateInterval;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            updateDashboard();
            updateInterval = setInterval(updateDashboard, 15000); // Mise à jour toutes les 15s
        });

        // Navigation entre sections
        function showSection(sectionName) {
            // Masquer toutes les sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });
            
            // Désactiver tous les onglets
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Afficher la section sélectionnée
            document.getElementById(sectionName).classList.add('active');
            event.target.classList.add('active');
            
            // Charger les données spécifiques à la section
            switch(sectionName) {
                case 'conversations':
                    chargerConversations();
                    break;
                case 'memory':
                    chargerZonesThermiques();
                    break;
            }
        }

        // Mise à jour du dashboard
        async function updateDashboard() {
            try {
                const response = await fetch('/api/status-ultra-complet');
                const data = await response.json();
                
                if (data.success) {
                    // Mettre à jour les métriques
                    if (data.qi_total) {
                        document.getElementById('qi-total').textContent = data.qi_total;
                    }
                    
                    // Mettre à jour les indicateurs système
                    updateSystemIndicators(data.indicateurs);
                    
                    // Mettre à jour les barres de progression
                    if (data.modules && data.modules.monitoring) {
                        const cpu = Math.round(data.modules.monitoring.cpu);
                        const ram = Math.round(data.modules.monitoring.ram);
                        
                        document.getElementById('cpu-usage').textContent = cpu + '%';
                        document.getElementById('ram-usage').textContent = ram + '%';
                        document.getElementById('cpu-bar').style.width = cpu + '%';
                        document.getElementById('cpu-bar').textContent = cpu + '%';
                        document.getElementById('ram-bar').style.width = ram + '%';
                        document.getElementById('ram-bar').textContent = ram + '%';
                    }
                }
            } catch (error) {
                console.error('Erreur mise à jour dashboard:', error);
            }
        }

        // Mettre à jour les indicateurs système
        function updateSystemIndicators(indicateurs) {
            if (!indicateurs) return;
            
            Object.entries(indicateurs).forEach(([nom, indicateur]) => {
                const element = document.getElementById(nom + '-indicator');
                if (element) {
                    const light = element.querySelector('.indicator-light');
                    
                    // Mettre à jour la couleur
                    light.className = `indicator-light ${indicateur.status === 'vert' ? 'green' : indicateur.status === 'orange' ? 'orange' : 'red'}`;
                    
                    // Mettre à jour la classe de l'indicateur
                    element.className = `indicator ${indicateur.status === 'vert' ? 'online' : indicateur.status === 'orange' ? 'warning' : 'offline'}`;
                }
            });
        }

        // Charger les conversations
        async function chargerConversations() {
            try {
                const response = await fetch('/api/conversations');
                const data = await response.json();
                
                if (data.success) {
                    const container = document.getElementById('conversations-list');
                    container.innerHTML = '';
                    
                    if (data.conversations.length === 0) {
                        container.innerHTML = '<div class="conversation-item"><div class="conversation-title">Aucune conversation</div></div>';
                        return;
                    }
                    
                    data.conversations.forEach(conv => {
                        const item = document.createElement('div');
                        item.className = 'conversation-item';
                        item.innerHTML = `
                            <div class="conversation-title">${conv.titre}</div>
                            <div class="conversation-meta">
                                ${conv.statistiques.nombreMessages} messages - 
                                ${new Date(conv.derniereModification).toLocaleString()}
                            </div>
                        `;
                        item.onclick = () => ouvrirConversation(conv.id);
                        container.appendChild(item);
                    });
                    
                    // Mettre à jour les statistiques
                    document.getElementById('total-conversations').textContent = data.total;
                    
                    const totalMessages = data.conversations.reduce((sum, conv) => sum + conv.statistiques.nombreMessages, 0);
                    document.getElementById('total-messages').textContent = totalMessages;
                }
            } catch (error) {
                console.error('Erreur chargement conversations:', error);
            }
        }

        // Actions système
        function redemarrerOllama() {
            if (confirm('Redémarrer Ollama ? Cela peut prendre quelques secondes.')) {
                // Implémenter le redémarrage
                alert('Redémarrage d\'Ollama en cours...');
            }
        }

        function testerConnexions() {
            alert('Test des connexions en cours...');
            updateDashboard();
        }

        function creerSauvegarde() {
            if (confirm('Créer une sauvegarde complète du système ?')) {
                fetch('/api/sauvegarde/complete', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('Sauvegarde créée avec succès !');
                        } else {
                            alert('Erreur lors de la sauvegarde: ' + data.error);
                        }
                    });
            }
        }

        function nouvelleConversation() {
            const titre = prompt('Titre de la nouvelle conversation:');
            if (titre) {
                fetch('/api/conversations/nouvelle', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ titre: titre })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        chargerConversations();
                        alert('Conversation créée !');
                    }
                });
            }
        }

        function ouvrirConversation(id) {
            window.open(`/chat?conversation=${id}`, '_blank');
        }

        // Charger les zones thermiques
        async function chargerZonesThermiques() {
            try {
                const response = await fetch('/api/status-ultra-complet');
                const data = await response.json();
                
                if (data.success && data.modules && data.modules.memoireThermique) {
                    const container = document.getElementById('thermal-zones');
                    container.innerHTML = '';
                    
                    Object.entries(data.modules.memoireThermique.zones).forEach(([nom, zone]) => {
                        const zoneDiv = document.createElement('div');
                        zoneDiv.className = 'metric';
                        zoneDiv.innerHTML = `
                            <span><strong>${nom}</strong> (${zone.type}):</span>
                            <span class="metric-value">${zone.temperature}°C - ${zone.frequence}Hz</span>
                        `;
                        container.appendChild(zoneDiv);
                    });
                }
            } catch (error) {
                console.error('Erreur chargement zones thermiques:', error);
            }
        }

        // Fonctions de paramètres (placeholder)
        function toggleDebug() { alert('Mode debug basculé'); }
        function toggleAutoSave() { alert('Sauvegarde auto basculée'); }
        function toggleNotifications() { alert('Notifications basculées'); }
        function reinitialiserSysteme() { 
            if (confirm('ATTENTION: Réinitialiser complètement le système ?')) {
                alert('Réinitialisation en cours...');
            }
        }
    </script>
</body>
</html>
