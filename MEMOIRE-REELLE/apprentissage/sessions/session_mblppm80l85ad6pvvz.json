{"id": "mblppm80l85ad6pvvz", "type": "resolution_problemes", "debut": "2025-06-07T04:06:31.104Z", "duree_prevue": 180000, "qi_debut": 162, "objectifs": ["Résoudre problèmes complexes", "Optimiser méthodes", "Innover solutions"], "progres": [{"probleme": "Problème de complexité 1", "complexite": 1, "solution": "approche_systematique", "temps": 586.2990763643551}, {"probleme": "Problème de complexité 2", "complexite": 2, "solution": "approche_systematique", "temps": 653.8986734100699}, {"probleme": "Problème de complexité 5", "complexite": 5, "solution": "approche_systematique", "temps": 468.56143906136316}, {"probleme": "Problème de complexité 6", "complexite": 6, "solution": "approche_systematique", "temps": 904.0684994175971}], "fin": "2025-06-07T04:06:32.314Z", "duree_reelle": 1210, "qi_fin": 162, "gain_qi": 0}