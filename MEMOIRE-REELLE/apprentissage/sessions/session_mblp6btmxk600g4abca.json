{"id": "mblp6btmxk600g4abca", "type": "resolution_problemes", "debut": "2025-06-07T03:51:31.162Z", "duree_prevue": 180000, "qi_debut": 162, "objectifs": ["Résoudre problèmes complexes", "Optimiser méthodes", "Innover solutions"], "progres": [{"probleme": "Problème de complexité 1", "complexite": 1, "solution": "approche_systematique", "temps": 644.9960445058999}, {"probleme": "Problème de complexité 2", "complexite": 2, "solution": "approche_systematique", "temps": 243.0938802463435}, {"probleme": "Problème de complexité 3", "complexite": 3, "solution": "approche_systematique", "temps": 761.2070992194772}, {"probleme": "Problème de complexité 4", "complexite": 4, "solution": "approche_systematique", "temps": 1036.1259164178891}, {"probleme": "Problème de complexité 5", "complexite": 5, "solution": "approche_systematique", "temps": 777.3202630131781}, {"probleme": "Problème de complexité 7", "complexite": 7, "solution": "approche_systematique", "temps": 986.770254430121}], "fin": "2025-06-07T03:51:32.370Z", "duree_reelle": 1208, "qi_fin": 162, "gain_qi": 0}